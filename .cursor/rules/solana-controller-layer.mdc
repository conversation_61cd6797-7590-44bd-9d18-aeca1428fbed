---
description:
globs:
alwaysApply: false
---
# Solana Controller Layer

## Core Controllers

### MetaSolanaCstaddressinfoController
The main controller for Solana customer wallet operations:
- [MetaSolanaCstaddressinfoController.java](mdc:Sava-RuoYi/ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/controller/MetaSolanaCstaddressinfoController.java)
- Handles HTTP requests for wallet management
- Implements security and validation

## API Endpoints
1. Wallet Management
   - GET /sol/solanaCstaddressinfo/list - List wallets
   - GET /sol/solanaCstaddressinfo/{id} - Get wallet details
   - POST /sol/solanaCstaddressinfo - Create wallet
   - PUT /sol/solanaCstaddressinfo - Update wallet
   - DELETE /sol/solanaCstaddressinfo/{ids} - Delete wallets

2. Wallet Generation
   - POST /sol/solanaCstaddressinfo/generate - Generate new wallet

## Security Features
- Uses Sa-Token for authentication
- Implements permission checks
- Validates input data
- Prevents duplicate submissions

## Best Practices
1. Use proper request validation
2. Implement proper error handling
3. Use appropriate HTTP methods
4. Follow RESTful API design
5. Implement proper security measures
