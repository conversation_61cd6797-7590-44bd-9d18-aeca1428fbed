---
description:
globs:
alwaysApply: false
---
# Solana WebSocket Architecture

## Core Components

### SolanaAccountWatcherFacade
The main interface for Solana account monitoring:
- [SolanaAccountWatcherFacade.java](mdc:Sava-RuoYi/ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/websocket/SolanaAccountWatcherFacade.java)
- Provides unified access to all monitoring functionality
- Implements ApplicationRunner for system startup integration

### Component Breakdown
1. WebSocket Management
   - [SolanaWebSocketManager.java](mdc:Sava-RuoYi/ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/websocket/SolanaWebSocketManager.java)
   - Handles connection lifecycle and message delivery

2. Address Subscription
   - [SolanaAddressSubscriber.java](mdc:Sava-RuoYi/ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/websocket/SolanaAddressSubscriber.java)
   - Manages address subscriptions and status

3. Transaction Processing
   - [SolanaTransactionProcessor.java](mdc:Sava-RuoYi/ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/websocket/SolanaTransactionProcessor.java)
   - Handles incoming transaction messages

## Migration Guide
For migrating from the deprecated SolanaAccountWatcher:
- [SolanaAccountWatcher迁移指南.md](mdc:Sava-RuoYi/ruoyi-modules/ruoyi-sol/docs/SolanaAccountWatcher迁移指南.md)

## Implementation Details
- All components follow interface-based design
- Implementation classes are in the `impl` package
- Uses Spring dependency injection
- Implements proper error handling and reconnection logic

## Best Practices
1. Always use SolanaAccountWatcherFacade for new code
2. Follow the migration guide for existing code
3. Implement proper error handling
4. Use the provided interfaces for extensibility
