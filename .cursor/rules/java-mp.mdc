---
description:
globs:
alwaysApply: false
---
# Java开发规范

## 代码风格与结构
- 编写清晰、高效且文档完善的Java代码，附带准确的Spring Boot示例
- 在代码中始终遵循Spring Boot最佳实践和约定
- 创建Web服务时实现RESTful API设计模式
- 使用符合驼峰命名法的描述性方法和变量名
- Spring Boot应用结构：控制器、服务、映射器、模型、配置

## Spring Boot细节
- 使用Spring Boot starter进行快速项目设置和依赖管理
- 正确使用注解（如@SpringBootApplication、@RestController、@Service）
- 有效利用Spring Boot的自动配置功能
- 使用@ControllerAdvice和@ExceptionHandler实现适当的异常处理

## 命名约定
- 类名使用Pascal命名法（如UserController、OrderService）
- 方法和变量名使用驼峰命名法（如findUserById、isOrderValid）
- 常量使用全大写（如MAX_RETRY_ATTEMPTS、DEFAULT_PAGE_SIZE）

## Java和Spring Boot使用
- 适当使用Java 17或更高版本特性（如records、sealed classes、模式匹配）
- 利用Spring Boot 3.x特性和最佳实践
- 适用时使用MyBatis-Plus进行数据库操作
- 使用Bean Validation实现适当的验证（如@Valid、自定义验证器）

## 配置和属性
- 使用application.properties或application.yml进行配置
- 使用Spring Profiles实现特定环境的配置
- 使用@ConfigurationProperties实现类型安全的配置属性

## 依赖注入和IoC
- 使用构造函数注入而非字段注入以提高可测试性
- 利用Spring的IoC容器管理bean生命周期

## 测试
- 使用JUnit 5和Spring Boot Test编写单元测试
- 使用MockMvc测试Web层
- 使用@SpringBootTest实现集成测试
- 使用@MybatisTest或@SpringBootTest进行映射层测试

## 性能和可扩展性
- 使用Spring Cache抽象实现缓存策略
- 使用@Async进行非阻塞操作的异步处理
- 实现适当的数据库索引和查询优化

## 安全性
- 使用Spring Security实现身份验证和授权
- 使用适当的密码编码（如BCrypt）
- 必要时实现CORS配置

## 日志和监控
- 使用SLF4J和Logback进行日志记录
- 实现适当的日志级别（ERROR、WARN、INFO、DEBUG）
- 使用Spring Boot Actuator进行应用监控和指标收集

## API文档
- 使用Springdoc OpenAPI（前身为Swagger）进行API文档编制

## 数据访问和ORM
- 使用MyBatis-Plus进行数据库操作
- 通过服务层逻辑或自定义映射器SQL实现适当的实体关系和级联
- 使用Flyway或Liquibase等工具进行数据库迁移

## 构建和部署
- 使用Maven进行依赖管理和构建流程
- 为不同环境（开发、测试、生产）实现适当的配置文件
- 适用时使用Docker进行容器化

## 最佳实践
- RESTful API设计（正确使用HTTP方法、状态码等）
- 微服务架构（如适用）
- 使用Spring的@Async或Spring WebFlux的响应式编程进行异步处理

在Spring Boot应用设计中遵循SOLID原则，保持高内聚低耦合。
