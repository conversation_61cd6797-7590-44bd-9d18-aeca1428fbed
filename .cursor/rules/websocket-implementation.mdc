---
description:
globs:
alwaysApply: false
---
# WebSocket Implementation Guide

## Core Components

### SolanaAccountWatcher
The main WebSocket implementation for monitoring Solana accounts:

- Handles WebSocket connection lifecycle
- Manages account subscriptions
- Processes incoming messages
- Implements reconnection logic
- Handles error scenarios

Key features:
- Automatic reconnection with exponential backoff
- Message queue for reliable delivery
- Thread-safe operations
- Resource cleanup on shutdown

## Best Practices

### Connection Management
1. Always use try-with-resources for WebSocket connections
2. Implement proper connection lifecycle management
3. Handle reconnection scenarios gracefully
4. Clean up resources properly on shutdown

### Error Handling
1. Use custom exceptions for WebSocket-specific errors
2. Implement proper error recovery mechanisms
3. Log errors with appropriate context
4. Handle connection interruptions gracefully

### Message Processing
1. Validate incoming messages
2. Process messages asynchronously when possible
3. Implement proper message queuing
4. Handle message timeouts appropriately

### Resource Management
1. Close connections properly
2. Clean up thread resources
3. Release system resources
4. Handle memory efficiently

## Code Examples

### Connection Setup
```java
try (WebSocketClient client = new WebSocketClient(uri)) {
    client.connect();
    // Handle connection
} catch (WebSocketException e) {
    // Handle error
}
```

### Message Processing
```java
private void processMessage(String message) {
    try {
        // Validate message
        // Process message
        // Handle result
    } catch (Exception e) {
        // Handle error
    }
}
```

### Error Recovery
```java
private void handleConnectionError() {
    // Implement exponential backoff
    // Attempt reconnection
    // Handle failure
}
```
