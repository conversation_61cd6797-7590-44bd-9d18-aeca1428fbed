---
description:
globs:
alwaysApply: false
---
# Project Structure Guide

## Core Modules
- `ruoyi-admin`: Main application entry point and admin interface
- `ruoyi-common`: Shared utilities and common code
- `ruoyi-modules`: Business modules
  - `ruoyi-sol`: Solana blockchain integration module
- `ruoyi-extend`: Extended functionality

## Solana Module Structure
The Solana module (`ruoyi-sol`) follows a clean architecture pattern:

- `controller/`: REST API endpoints
- `service/`: Business logic implementation
- `domain/`: Entity classes
- `mapper/`: Database access layer
- `websocket/`: WebSocket implementations
- `exception/`: Custom exception classes
- `util/`: Utility classes
- `config/`: Configuration classes
- `constants/`: Constant definitions
- `enums/`: Enumeration types
- `dto/`: Data Transfer Objects
- `event/`: Event handling
- `instruction/`: Blockchain instruction processing
- `scan/`: Blockchain scanning functionality
- `manager/`: Business logic managers
- `task/`: Scheduled tasks

## Key Files
- Main WebSocket implementation: [SolanaAccountWatcher.java](mdc:ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/websocket/SolanaAccountWatcher.java)
- Custom exceptions: [WebSocketException.java](mdc:ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/exception/WebSocketException.java)
- Configuration: [WebSocketConfig.java](mdc:ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/config/WebSocketConfig.java)
