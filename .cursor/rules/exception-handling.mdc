---
description:
globs:
alwaysApply: false
---
# Exception Handling Guide

## Custom Exceptions

### WebSocket Exceptions
- `WebSocketException`: Base exception for WebSocket-related errors
- `WebSocketConnectionException`: Connection-specific errors
- `WebSocketMessageException`: Message processing errors
- `WebSocketSubscriptionException`: Subscription-related errors

## Exception Handling Patterns

### 1. Resource Management
```java
try (AutoCloseable resource = getResource()) {
    // Use resource
} catch (Exception e) {
    // Handle error
}
```

### 2. Specific Exception Handling
```java
try {
    // Operation
} catch (WebSocketConnectionException e) {
    // Handle connection error
} catch (WebSocketMessageException e) {
    // Handle message error
} catch (WebSocketException e) {
    // Handle general WebSocket error
}
```

### 3. Exception Propagation
```java
public void method() throws WebSocketException {
    try {
        // Operation
    } catch (Exception e) {
        throw new WebSocketException("Error message", e);
    }
}
```

## Best Practices

1. **Use Specific Exceptions**
   - Avoid catching generic Exception
   - Create custom exceptions for specific error cases
   - Include meaningful error messages

2. **Resource Cleanup**
   - Always use try-with-resources for AutoCloseable resources
   - Ensure proper cleanup in finally blocks
   - Handle InterruptedException properly

3. **Error Logging**
   - Log exceptions with appropriate context
   - Include stack traces for debugging
   - Use appropriate log levels

4. **Exception Recovery**
   - Implement retry mechanisms where appropriate
   - Handle transient errors gracefully
   - Provide fallback behavior when possible

## Common Patterns

### Retry Pattern
```java
public void operationWithRetry() {
    int maxRetries = 3;
    int retryCount = 0;

    while (retryCount < maxRetries) {
        try {
            // Operation
            return;
        } catch (WebSocketException e) {
            retryCount++;
            if (retryCount == maxRetries) {
                throw e;
            }
            // Wait before retry
        }
    }
}
```

### Fallback Pattern
```java
public void operationWithFallback() {
    try {
        // Primary operation
    } catch (WebSocketException e) {
        // Fallback operation
    }
}
```
