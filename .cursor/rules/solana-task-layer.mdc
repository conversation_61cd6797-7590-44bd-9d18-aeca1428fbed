---
description:
globs:
alwaysApply: false
---
# Solana Task Layer

## Core Tasks

### SolanaRetryTask
Handles retry logic for failed address subscriptions:
- [SolanaRetryTask.java](mdc:Sava-RuoYi/ruoyi-modules/ruoyi-sol/src/main/java/org/dromara/sol/task/SolanaRetryTask.java)
- Implements scheduled retry mechanism
- Uses rate limiting for API calls

## Task Features
1. Retry Mechanism
   - Periodic retry of failed subscriptions
   - Rate limiting to prevent API overload
   - Status tracking and management

2. Error Handling
   - Graceful failure handling
   - Logging and monitoring
   - Recovery mechanisms

## Implementation Details
- Uses Spring's @Scheduled for task scheduling
- Implements proper rate limiting
- Uses Redis for state management
- Follows best practices for task execution

## Best Practices
1. Implement proper error handling
2. Use rate limiting for API calls
3. Implement proper logging
4. Handle task failures gracefully
5. Use appropriate scheduling intervals
