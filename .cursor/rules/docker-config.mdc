---
description:
globs:
alwaysApply: false
---
# Docker 配置说明

## 网络模式

项目提供了两种Docker网络模式配置：

1. **Host模式** - [script/docker/docker-compose.yml](mdc:script/docker/docker-compose.yml)
   - 所有容器直接使用宿主机网络
   - 容器间通过localhost/127.0.0.1通信
   - 无需额外端口映射（与宿主机共享端口）

2. **Bridge模式** - [script/docker/docker-compose-bridge.yml](mdc:script/docker/docker-compose-bridge.yml)
   - 容器使用独立网络，通过自定义bridge网络`ruoyi-net`连接
   - 容器间通过服务名互相访问（如`mysql`、`redis`）
   - 需要显式配置端口映射

## 存储卷管理

配置文件使用两种主要的卷管理方式：

1. **绑定挂载（推荐）**：
   ```yaml
   volumes:
     - /docker/nginx/conf/nginx.conf:/etc/nginx/nginx.conf
   ```
   直接映射宿主机文件到容器，修改实时同步

2. **Docker卷（部分使用）**：
   适用于数据存储，不需要频繁修改的场景

## 主要服务组件

项目包含以下关键服务：
- MySQL - 数据库服务
- Redis - 缓存服务
- Nginx - Web服务器
- MinIO - 对象存储服务
- RuoYi服务器 - 应用服务组件
- 监控服务 - 系统监控组件
- SnailJob - 任务调度服务
