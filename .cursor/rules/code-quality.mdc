---
description:
globs:
alwaysApply: false
---
# Code Quality Standards

## General Principles

1. **Clean Code**
   - Meaningful variable and method names
   - Single responsibility principle
   - DRY (Don't Repeat Yourself)
   - KISS (Keep It Simple, Stupid)

2. **Code Organization**
   - Logical package structure
   - Clear separation of concerns
   - Consistent file organization
   - Proper use of access modifiers

3. **Documentation**
   - Clear method documentation
   - Class-level documentation
   - Package-level documentation
   - Inline comments for complex logic

## WebSocket-Specific Standards

### 1. Connection Management
```java
// Good
try (WebSocketClient client = new WebSocketClient(uri)) {
    client.connect();
    // Handle connection
}

// Bad
WebSocketClient client = new WebSocketClient(uri);
client.connect();
// Missing resource cleanup
```

### 2. Error Handling
```java
// Good
try {
    // Operation
} catch (WebSocketConnectionException e) {
    log.error("Connection error: {}", e.getMessage(), e);
    // Handle error
}

// Bad
try {
    // Operation
} catch (Exception e) {
    // Generic exception handling
}
```

### 3. Resource Cleanup
```java
// Good
@Override
public void close() {
    try {
        // Cleanup resources
    } finally {
        // Ensure cleanup
    }
}

// Bad
public void close() {
    // Missing cleanup
}
```

## Code Review Checklist

1. **Functionality**
   - [ ] Code meets requirements
   - [ ] Edge cases handled
   - [ ] Error scenarios covered
   - [ ] Resource management proper

2. **Code Quality**
   - [ ] Follows coding standards
   - [ ] No code duplication
   - [ ] Proper exception handling
   - [ ] Clear and maintainable

3. **Performance**
   - [ ] Efficient resource usage
   - [ ] No memory leaks
   - [ ] Proper concurrency handling
   - [ ] Scalable design

4. **Security**
   - [ ] Input validation
   - [ ] Secure resource handling
   - [ ] Proper error messages
   - [ ] No sensitive data exposure

## Common Anti-patterns to Avoid

1. **Resource Leaks**
   - Not closing resources
   - Missing finally blocks
   - Improper exception handling

2. **Error Handling**
   - Catching generic Exception
   - Swallowing exceptions
   - Inadequate error logging

3. **Code Structure**
   - Too many responsibilities
   - Deep nesting
   - Complex conditionals
   - Long methods

4. **Concurrency**
   - Race conditions
   - Deadlocks
   - Improper synchronization
   - Thread safety issues
