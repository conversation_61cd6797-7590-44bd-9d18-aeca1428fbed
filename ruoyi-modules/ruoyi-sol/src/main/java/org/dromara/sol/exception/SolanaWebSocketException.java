package org.dromara.sol.exception;

/**
 * Solana WebSocket异常基类
 * 用于处理与Solana WebSocket连接和交互相关的异常
 */
public class SolanaWebSocketException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public SolanaWebSocketException(String message) {
        super(message);
    }

    public SolanaWebSocketException(String message, Throwable cause) {
        super(message, cause);
    }

    public SolanaWebSocketException(Throwable cause) {
        super(cause);
    }
}
