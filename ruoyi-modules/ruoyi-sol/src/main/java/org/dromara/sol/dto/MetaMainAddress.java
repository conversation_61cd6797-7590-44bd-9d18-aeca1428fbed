package org.dromara.sol.dto;

import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

/**
 * 主钱包地址
 *
 * <AUTHOR>
 * @date 2025/5/9 13:14
 **/
@Getter
@Setter
public class MetaMainAddress {

    /**
     * 商户id
     */
    private String sysId;

    /**
     * 类型（BEP20/TRC20/SOL）
     */
    private String type;

    /**
     * 主钱包地址
     */
    private String mainaddress;

    /**
     * 最低充值
     */
    private BigDecimal minimumRecharge;

    /**
     * 归集阈值
     */
    private BigDecimal collectThreshold;


    /**
     * 告警阈值
     */
    private BigDecimal alarmThreshold;

}

