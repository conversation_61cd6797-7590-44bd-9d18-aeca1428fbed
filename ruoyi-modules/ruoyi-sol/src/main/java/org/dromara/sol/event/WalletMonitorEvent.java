package org.dromara.sol.event;

import lombok.Getter;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.springframework.context.ApplicationEvent;

/**
 * 钱包监控事件
 * 之后可以用mq实现集群监听
 *
 * <AUTHOR>
 */
@Getter
public class WalletMonitorEvent extends ApplicationEvent {

    private final MetaSolanaCstaddressinfoVo wallet;

    public WalletMonitorEvent(Object source, MetaSolanaCstaddressinfoVo wallet) {
        super(source);
        this.wallet = wallet;
    }
}
