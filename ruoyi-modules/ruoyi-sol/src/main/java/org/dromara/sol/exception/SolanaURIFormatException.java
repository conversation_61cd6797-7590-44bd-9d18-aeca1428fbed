package org.dromara.sol.exception;

import java.net.URISyntaxException;

/**
 * Solana URI格式异常
 * 用于处理WebSocket URL格式错误的异常
 */
public class SolanaURIFormatException extends SolanaWebSocketException {

    private static final long serialVersionUID = 1L;

    public SolanaURIFormatException(String message) {
        super(message);
    }

    public SolanaURIFormatException(String message, URISyntaxException cause) {
        super(message, cause);
    }

    public SolanaURIFormatException(URISyntaxException cause) {
        super("Solana WebSocket URI格式错误: " + cause.getMessage(), cause);
    }
}
