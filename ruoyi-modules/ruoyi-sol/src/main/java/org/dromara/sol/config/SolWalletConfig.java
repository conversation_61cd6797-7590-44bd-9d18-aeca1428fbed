package org.dromara.sol.config;

import lombok.Getter;
import lombok.Setter;
import org.dromara.sol.dto.MetaMainAddress;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Solana钱包配置
 *
 * <AUTHOR>
 * @date 2023/5/30
 **/
@Setter
@Getter
@Configuration
@ConfigurationProperties(prefix = "sol.wallet")
public class SolWalletConfig {

    // funding钱包私钥
    private String fundingPrivateKey;

    // 主钱包地址列表
    private List<MetaMainAddress> mainAddressList;

    /**
     * 通过sysId获取对应主钱包
     *
     * @param sysId 系统ID
     * @return 主钱包地址，如果不存在则返回null
     */
    public MetaMainAddress getMainAddressBySysId(String sysId) {
        if (mainAddressList == null || mainAddressList.isEmpty() || sysId == null) {
            return null;
        }

        for (MetaMainAddress item : mainAddressList) {
            if (sysId.equals(item.getSysId())) {
                return item;
            }
        }
        return null;
    }
}
