package org.dromara.sol.manager;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.lock.annotation.Lock4j;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.sol.config.SolWalletConfig;
import org.dromara.sol.domain.MetaSolanaCstaddressinfo;
import org.dromara.sol.domain.bo.MetaSolanaTransactionBo;
import org.dromara.sol.domain.vo.MetaSolanaTransactionVo;
import org.dromara.sol.dto.MetaMainAddress;
import org.dromara.sol.dto.SolTransactionModel;
import org.dromara.sol.enums.SolCoinType;
import org.dromara.sol.mq.MqMonitorDto;
import org.dromara.sol.mq.NormalRabbitProducer;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.dromara.sol.service.IMetaSolanaTransactionsService;
import org.p2p.solanaj.rpc.types.ConfirmedTransaction;
import org.p2p.solanaj.rpc.types.TokenResultObjects;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * sol交易记录处理
 *
 * <AUTHOR>
 * @date 2025/4/27 14:57
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class SolTransactionManager {

    private final SolWalletConfig walletConfig;
    private final SolHttpManager solHttpManager;
    private final MetaHttpManager metaHttpManager;
    private final IMetaSolanaTransactionsService solanaTransactionsService;
    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;
    private final NormalRabbitProducer normalRabbitProducer;

    /**
     * 处理交易（带地址信息）
     *
     * @param transaction 交易
     * @param userAddress 关联地址（可为null）
     */
    public void call(ConfirmedTransaction transaction, String userAddress) {
        //从meta中找到用户数据，以及另一个账户数据
        SolTransactionModel solTransactionModel = SolTransactionModel.builder()
            .setSolBlock(solHttpManager.getSlotContent(transaction.getSlot()))
            .setTransactionObject(transaction)
            .build();

        // 使用新方法解析交易
        List<MetaSolanaTransactionBo> transactionBoList = parseTransactionByBalanceChanges(solTransactionModel, userAddress);
        if (transactionBoList.isEmpty()) {
            log.info("在该交易中没有找到有效转账:{}", transaction.getTransaction().getSignatures().getFirst());
            return;
        }

        // 处理解析到的所有交易
        for (MetaSolanaTransactionBo transactionBo : transactionBoList) {
            // 如果提供了用户地址，尝试匹配交易的接收或发送地址
            if (userAddress != null) {
                if (userAddress.equals(transactionBo.getAddress())) {
                    // 用户地址是接收方，处理为收款交易
                    processReceiveTransaction(transactionBo);
                    continue;
                } else if (userAddress.equals(transactionBo.getFromaddress())) {
                    // 用户地址是发送方，处理为归集或发送交易
                    processSendTransaction(transactionBo);
                    continue;
                }
            }

            // 如果未提供用户地址或者用户地址不匹配，使用原有逻辑处理
            //用户充值
            if (transactionBo.getType().equals("receive")) {
                processReceiveTransaction(transactionBo);
            }
            //用户钱包归集到主钱包
            else if (solanaCstaddressinfoService.queryAllAddressSet().contains(transactionBo.getFromaddress())) {
                processSendTransaction(transactionBo);
            }
            //莫名其妙的收入
            else {
                transactionBo.setType("send");
                transactionBo.setIssync(2);
                solanaTransactionsService.insertByBo(transactionBo);
            }
        }
    }

    /**
     * 处理接收交易
     *
     * @param transactionBo 交易数据
     */
    private void processReceiveTransaction(MetaSolanaTransactionBo transactionBo) {
        solanaTransactionsService.insertByBo(transactionBo);
        // 获取该地址对应的钱包信息
        MetaSolanaCstaddressinfo oneByAddress = solanaCstaddressinfoService.getOneByAddress(transactionBo.getAddress());
        // 主钱包地址
        MetaMainAddress metaMainAddress = walletConfig.getMainAddressBySysId(oneByAddress.getSysId());
        if (metaMainAddress == null) {
            throw new BaseException("主钱包地址不存在,sysId:" + oneByAddress.getSysId());
        }
        // tg告警
        alarm(transactionBo, oneByAddress, metaMainAddress);
        // 判断是否信任
        if (!transactionBo.getTrustworthy()){
            log.info("不入账：交易可信度为false,sysId:{},txId:{}"
                , oneByAddress.getSysId()
                , transactionBo.getTxid());
            return;
        }
        // 判断金额
        if (isAmountBelowMinimumRecharge(transactionBo.getAmount(), metaMainAddress.getMinimumRecharge())) {
            log.info("不入账：入账金额:{}小于最小入账金额:{},sysId:{},txId:{}"
                , transactionBo.getAmount()
                , metaMainAddress.getMinimumRecharge()
                , oneByAddress.getSysId()
                , transactionBo.getTxid());
            return;
        }

        //入账
        SpringUtil.getBean(this.getClass()).doData(transactionBo.getId(), true);
    }

    /**
     * 判断金额是否小于最小入账金额
     *
     * @param amount          交易金额
     * @param minimumRecharge 最小入账金额
     * @return 如果交易金额小于最小入账金额则返回true
     */
    public boolean isAmountBelowMinimumRecharge(BigDecimal amount, BigDecimal minimumRecharge) {
        return amount.compareTo(minimumRecharge) < 0;
    }

    /**
     * 处理发送交易
     *
     * @param transactionBo 交易数据
     */
    private void processSendTransaction(MetaSolanaTransactionBo transactionBo) {
        transactionBo.setType("collect");
        transactionBo.setIssync(2);
        solanaTransactionsService.insertByBo(transactionBo);
        // 获取该地址对应的钱包信息
        MetaSolanaCstaddressinfo userWallet = solanaCstaddressinfoService.getOneByAddress(transactionBo.getFromaddress());
        // 主钱包地址
        MetaMainAddress metaMainAddress = walletConfig.getMainAddressBySysId(userWallet.getSysId());
        if (metaMainAddress == null) {
            throw new BaseException("主钱包地址不存在,sysId:" + userWallet.getSysId());
        }
        // tg告警
        alarm(transactionBo, userWallet, metaMainAddress);
        // 入账处理
        SpringUtil.getBean(this.getClass()).doData(transactionBo.getId(), true);
    }

    private void alarm(MetaSolanaTransactionBo transactionBo, MetaSolanaCstaddressinfo userWallet, MetaMainAddress metaMainAddress) {
        // 获取token余额
        TokenResultObjects.TokenAmountInfo tokenBalance = solHttpManager.getTokenBalance(userWallet.getCstAddress(), transactionBo.getContract());
        BigDecimal uiAmount = new BigDecimal(tokenBalance.getUiAmountString());

        // 是否告警(alarmThreshold)
        if (uiAmount.compareTo(metaMainAddress.getAlarmThreshold()) >= 0) {
            //获取sol余额
            BigInteger solAmount = solHttpManager.getBalance(userWallet.getCstAddress());

            //币种
            SolCoinType solCoinType = SolCoinType.fromContractAddress(transactionBo.getContract());

            String userAddress = transactionBo.getType().equals("receive") ? transactionBo.getAddress() : transactionBo.getFromaddress();
            String otherAddress = transactionBo.getType().equals("receive") ? transactionBo.getFromaddress() : transactionBo.getAddress();
            MqMonitorDto sol = new MqMonitorDto(
                transactionBo.getTimestamp(),
                "SOL",
                userAddress,
                otherAddress,
                transactionBo.getType(),
                transactionBo.getAmount(),
                solCoinType.getCode(),
                new BigDecimal(solAmount).divide(new BigDecimal("1000000000"), 9, RoundingMode.DOWN),
                SolCoinType.SOL.getCode(),
                uiAmount,
                solCoinType.getCode(),
                userWallet.getSysId());

            normalRabbitProducer.sendMonitor(sol);
        }
    }

    public void doData(Long transactionId, boolean needCheckAmount) {
        MetaSolanaTransactionVo metaSolanaTransactionVo = solanaTransactionsService.queryById(transactionId);
        // 指定时区
        Instant instant = Instant.ofEpochMilli(metaSolanaTransactionVo.getTimestamp() * 1000L);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of("Asia/Shanghai"));

        metaSolanaTransactionVo.setTime(Date.from(zonedDateTime.toInstant()));
        if (isTransactionAlreadyProcessed(metaSolanaTransactionVo.getIssync())) {
            throw new BaseException("这笔交易已经处理完成，请勿重复入账");
        }
        SolCoinType solCoinType = SolCoinType.fromContractAddress(metaSolanaTransactionVo.getContract());
        metaSolanaTransactionVo.setCoinType(solCoinType.getCode());
        MetaSolanaCstaddressinfo oneByAddress;
        if (metaSolanaTransactionVo.getType().equals("receive")) {
            oneByAddress = solanaCstaddressinfoService.getOneByAddress(metaSolanaTransactionVo.getAddress());
        } else {
            oneByAddress = solanaCstaddressinfoService.getOneByAddress(metaSolanaTransactionVo.getFromaddress());
        }
        metaSolanaTransactionVo.setCstId(oneByAddress.getCstId());

        if (metaSolanaTransactionVo.getType().equals("receive")) {
            //查找用户真实余额，如果余额低于入账
            if (needCheckAmount) {
                TokenResultObjects.TokenAmountInfo realBalance = solHttpManager.getTokenBalance(oneByAddress.getCstAddress(), solCoinType.getContractAddress());
                if (new BigDecimal(realBalance.getAmount()).compareTo(metaSolanaTransactionVo.getAmount()) < 0) {
                    log.warn("用户真实余额低于入账金额:{}", metaSolanaTransactionVo.getAmount());
                    return;
                }
            }
            if (metaHttpManager.coinData(metaSolanaTransactionVo)) {
                MetaSolanaTransactionBo updateBo = new MetaSolanaTransactionBo();
                updateBo.setId(transactionId);
                updateBo.setIssync(1);
                solanaTransactionsService.updateByBo(updateBo);

                // 归集功能(aggregationThreshold)
                doCollection(oneByAddress.getCstAddress(), solCoinType);
            }
        }
    }

    /**
     * 判断交易是否已经处理过
     *
     * @param issync 同步状态值
     * @return 如果已处理则返回true
     */
    public boolean isTransactionAlreadyProcessed(int issync) {
        return issync == 1;
    }

    public void doCollection(String address, SolCoinType solCoinType) {
        MetaSolanaCstaddressinfo oneByAddress = solanaCstaddressinfoService.getOneByAddress(address);
        //主钱包地址
        MetaMainAddress metaMainAddress = walletConfig.getMainAddressBySysId(oneByAddress.getSysId());
        if (metaMainAddress == null) {
            throw new BaseException("主钱包地址不存在,sysId:" + oneByAddress.getSysId());
        }

        //查询账户token代币余额
        TokenResultObjects.TokenAmountInfo tokenBalance = solHttpManager.getTokenBalance(oneByAddress.getCstAddress(), solCoinType.getContractAddress());
        if (new BigDecimal(tokenBalance.getUiAmountString()).compareTo(metaMainAddress.getCollectThreshold()) >= 0) {
            //查询账户sol余额
            BigInteger balance = solHttpManager.getBalance(oneByAddress.getCstAddress());

            //查询转账所需手续费sol，暂定为80000(1000000000)
            if (balance.compareTo(new BigInteger("1000000")) < 0) {
                //从备用钱包获取手续费
                String transferSignature = solHttpManager.transfer(walletConfig.getFundingPrivateKey(), oneByAddress.getCstAddress(), 1000000L);
                log.info("从备用钱包获取手续费成功，txid:{}", transferSignature);

                ThreadUtil.sleep(2000);
            }

            //发起token转账
            String hash = solHttpManager.transferToken(
                oneByAddress.getCstPrivate(),
                metaMainAddress.getMainaddress(),
                Long.parseLong(tokenBalance.getAmount()),
                solCoinType.getContractAddress()
            );
            log.info("转账成功，txid:{}", hash);
        }
    }

    /**
     * 处理交易
     */
    @Lock4j(keys = {"#signature"})
    public void callTransactionBySignature(String signature) {
        callTransactionBySignature(signature, null);
    }

    /**
     * 处理交易（带地址信息）
     *
     * @param signature 交易签名
     * @param address   关联地址（可为null）
     */
    @Lock4j(keys = {"#signature", "#address"})
    public void callTransactionBySignature(String signature, String address) {
        if (solanaTransactionsService.isExist(signature)) {
            log.debug("该交易已处理:{}", signature);
            return;
        }
        if (address != null) {
            log.info("处理交易: {}, 关联地址: {}", signature, address);
        }
        ConfirmedTransaction transaction = solHttpManager.getTransaction(signature);
        SpringUtil.getBean(this.getClass()).call(transaction, address);
    }

    /**
     * 通过分析代币余额变化来解析交易
     * 此方法不依赖于特定的programId，适用于监控各种交易所的转账
     *
     * @param solTransactionModel 交易模型
     * @return 解析后的交易信息列表
     */
    List<MetaSolanaTransactionBo> parseTransactionByBalanceChanges(SolTransactionModel solTransactionModel, String address) {
        List<MetaSolanaTransactionBo> resultList = new ArrayList<>();
        ConfirmedTransaction transaction = solTransactionModel.getTransactionObject();
        boolean isTrustworthy = true;

        // 获取正常账户列表
        List<String> accountKeys = transaction.getTransaction().getMessage().getAccountKeys();
        List<ConfirmedTransaction.TokenBalance> preBalances = transaction.getMeta().getPreTokenBalances();
        List<ConfirmedTransaction.TokenBalance> postBalances = transaction.getMeta().getPostTokenBalances();

        // 监控的地址列表
        HashSet<String> monitoredAddresses = new HashSet<>();
        if (StringUtils.isBlank(address)) {
            monitoredAddresses = solanaCstaddressinfoService.queryAllAddressSet();
        } else {
            monitoredAddresses.add(address);
        }
        // 监控的代币列表
        Set<String> mintAddresses = Arrays.stream(SolCoinType.values()).map(SolCoinType::getContractAddress).collect(Collectors.toSet());

        // 记录所有代币余额变化
        Map<String, Map<String, BigDecimal>> addressChanges = new HashMap<>();

        // 1. 计算所有账户的代币余额变化
        for (ConfirmedTransaction.TokenBalance post : postBalances) {
            String mint = post.getMint();

            // 如果指定了目标代币且当前代币不是目标代币，则跳过
            if (!mintAddresses.isEmpty() && !mintAddresses.contains(mint)) {
                continue;
            }

            // 增加边界检查，防止索引越界
            int accountIndex = post.getAccountIndex().intValue();
            // 跳过无效的账户索引
            if (accountIndex < 0 || accountIndex >= accountKeys.size()) {
                log.debug("账户索引越界: {} (数组大小: {})", accountIndex, accountKeys.size());
                continue;
            }

            String accountAddress = accountKeys.get(accountIndex);
            BigDecimal postAmount = new BigDecimal(post.getUiTokenAmount().getAmount());
            BigDecimal preAmount;

            // 找对应的交易后余额
            ConfirmedTransaction.TokenBalance pre = findMatchingPreBalance(post, preBalances);
            if (pre == null) {
                isTrustworthy = false;
                preAmount = new BigDecimal(0);
            } else {
                preAmount = new BigDecimal(pre.getUiTokenAmount().getAmount());
            }
            BigDecimal change = postAmount.subtract(preAmount);

            // 记录变化
            if (change.compareTo(BigDecimal.ZERO) != 0) {
                Map<String, BigDecimal> mintChanges = addressChanges.computeIfAbsent(accountAddress, k -> new HashMap<>());
                mintChanges.put(mint, change);
            }

        }

        // 2. 寻找我们监控的地址参与的交易
        for (String monitoredAddress : monitoredAddresses) {
            Map<String, BigDecimal> changes = addressChanges.get(monitoredAddress);
            if (changes == null || changes.isEmpty()) {
                continue; // 该监控地址没有参与交易
            }

            // 处理监控地址的每一种代币变化
            for (Map.Entry<String, BigDecimal> entry : changes.entrySet()) {
                String mint = entry.getKey();
                BigDecimal change = entry.getValue();

                MetaSolanaTransactionBo transactionBo = new MetaSolanaTransactionBo();
                transactionBo.setContract(mint);
                transactionBo.setTxid(transaction.getTransaction().getSignatures().getFirst());
                transactionBo.setBlockheight(solTransactionModel.getSolBlock() != null ?
                    (long) (solTransactionModel.getSolBlock().getParentSlot() + 1) : 0);
                transactionBo.setTimestamp(solTransactionModel.getSolBlock() != null ?
                    (long) solTransactionModel.getSolBlock().getBlockTime() : System.currentTimeMillis() / 1000);

                // 设置交易手续费
                transactionBo.setFee(BigDecimal.valueOf(transaction.getMeta().getFee())
                    .divide(new BigDecimal("1000000000"), 9, RoundingMode.DOWN));

                transactionBo.setTrustworthy(isTrustworthy);

                if (change.compareTo(BigDecimal.ZERO) > 0) {
                    // 监控地址收到代币 - 寻找发送方
                    String senderAddress = findAddressWithNegativeChange(addressChanges, mint, change.negate());

                    transactionBo.setType("receive");
                    transactionBo.setIssync(0);
                    transactionBo.setAmount(change.abs().divide(getTokenDecimalDivisor(mint), getTokenDecimals(mint), RoundingMode.DOWN));
                    transactionBo.setAddress(solHttpManager.parseAccountToSolAddress(monitoredAddress));
                    transactionBo.setFromaddress(senderAddress != null ? solHttpManager.parseAccountToSolAddress(senderAddress) : "unknown");

                    resultList.add(transactionBo);
                } else {
                    // 监控地址发送代币 - 寻找接收方
                    String receiverAddress = findAddressWithPositiveChange(addressChanges, mint, change.abs());

                    transactionBo.setType("send");
                    transactionBo.setIssync(2);
                    transactionBo.setAmount(change.abs().divide(getTokenDecimalDivisor(mint), getTokenDecimals(mint), RoundingMode.DOWN));
                    transactionBo.setAddress(receiverAddress != null ? solHttpManager.parseAccountToSolAddress(receiverAddress) : "unknown");
                    String monitoredMainAddress = solHttpManager.parseAccountToSolAddress(monitoredAddress);
                    transactionBo.setFromaddress(monitoredMainAddress);

                    resultList.add(transactionBo);
                }
            }
        }

        return resultList;
    }

    /**
     * 查找具有指定代币正向余额变化的地址
     */
    private String findAddressWithPositiveChange(Map<String, Map<String, BigDecimal>> addressChanges, String mint, BigDecimal amount) {
        if (mint == null || amount == null) {
            return null; // 添加参数检查
        }

        for (Map.Entry<String, Map<String, BigDecimal>> entry : addressChanges.entrySet()) {
            if (entry.getKey() == null || entry.getValue() == null) {
                continue; // 跳过无效条目
            }

            Map<String, BigDecimal> changes = entry.getValue();
            if (changes.containsKey(mint)) {
                BigDecimal change = changes.get(mint);
                // 添加空值检查
                if (change != null && change.compareTo(BigDecimal.ZERO) > 0 &&
                    isApproximatelyEqual(change, amount, new BigDecimal("0.001"))) {
                    return entry.getKey();
                }
            }
        }
        return null;
    }

    /**
     * 查找具有指定代币负向余额变化的地址
     */
    private String findAddressWithNegativeChange(Map<String, Map<String, BigDecimal>> addressChanges, String mint, BigDecimal amount) {
        for (Map.Entry<String, Map<String, BigDecimal>> entry : addressChanges.entrySet()) {
            Map<String, BigDecimal> changes = entry.getValue();
            if (changes.containsKey(mint)) {
                BigDecimal change = changes.get(mint);
                // 允许小误差（例如手续费）
                if (change.compareTo(BigDecimal.ZERO) < 0 &&
                    isApproximatelyEqual(change, amount, new BigDecimal("0.001"))) {
                    return entry.getKey();
                }
            }
        }
        return null;
    }

    /**
     * 检查两个数值是否近似相等（考虑误差）
     */
    private boolean isApproximatelyEqual(BigDecimal a, BigDecimal b, BigDecimal tolerance) {
        return a.subtract(b).abs().compareTo(tolerance) <= 0;
    }


    /**
     * 在preBalances中查找与postBalance对应的余额
     */
    private ConfirmedTransaction.TokenBalance findMatchingPreBalance(ConfirmedTransaction.TokenBalance post,
                                                                     List<ConfirmedTransaction.TokenBalance> preBalances) {
        if (post == null || preBalances == null || preBalances.isEmpty()) {
            return null; // 添加参数检查
        }

        for (ConfirmedTransaction.TokenBalance pre : preBalances) {
            if (pre == null || pre.getAccountIndex() == null || post.getAccountIndex() == null ||
                pre.getMint() == null || post.getMint() == null) {
                continue; // 跳过无效条目
            }

            if (pre.getAccountIndex().equals(post.getAccountIndex()) &&
                pre.getMint().equals(post.getMint())) {
                return pre;
            }
        }
        return null;
    }


    /**
     * 在postBalances中查找与preBalance对应的余额
     */
    private ConfirmedTransaction.TokenBalance findMatchingPostBalance(ConfirmedTransaction.TokenBalance pre,
                                                                      List<ConfirmedTransaction.TokenBalance> postBalances) {
        if (pre == null || postBalances == null || postBalances.isEmpty()) {
            return null; // 添加参数检查
        }

        for (ConfirmedTransaction.TokenBalance post : postBalances) {
            if (post == null || post.getAccountIndex() == null || pre.getAccountIndex() == null ||
                post.getMint() == null || pre.getMint() == null) {
                continue; // 跳过无效条目
            }

            if (post.getAccountIndex().equals(pre.getAccountIndex()) &&
                post.getMint().equals(pre.getMint())) {
                return post;
            }
        }
        return null;
    }

    /**
     * 获取代币小数位数
     */
    private int getTokenDecimals(String mint) {
        if (mint == null || mint.equalsIgnoreCase("SOL")) {
            return 9;
        }
        SolCoinType coinType = SolCoinType.fromContractAddress(mint);
        if (coinType != null) {
            return coinType.getDecimals();
        }
        log.warn("小数位数未找到，针对 mint: {}。将使用默认值6。", mint);
        return 6;
    }

    /**
     * 获取代币小数位除数
     */
    private BigDecimal getTokenDecimalDivisor(String mint) {
        int decimals = getTokenDecimals(mint);
        return BigDecimal.TEN.pow(decimals);
    }


}
