package org.dromara.sol.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.sol.domain.MetaSolanaCstaddressinfo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * Solana客户热钱包信息视图对象 meta_solana_cstaddressinfo
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MetaSolanaCstaddressinfo.class)
public class MetaSolanaCstaddressinfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @ExcelProperty(value = "ID标识")
    private Long id;

    /**
     * 客户ID
     */
    @ExcelProperty(value = "客户ID")
    private Long cstId;

    /**
     * 客户钱包地址
     */
    @ExcelProperty(value = "客户钱包地址")
    private String cstAddress;

//    /**
//     * 客户钱包私钥信息
//     */
//    @ExcelProperty(value = "客户钱包私钥信息")
//    private String cstPrivate;

    /**
     * 客户USDT钱包地址
     */
    @ExcelProperty(value = "客户USDT钱包地址")
    private String cstUsdtAddress;

    /**
     * 客户USDC钱包地址
     */
    @ExcelProperty(value = "客户USDC钱包地址")
    private String cstUsdcAddress;

    /**
     * 渠道
     */
    @ExcelProperty(value = "渠道")
    private String sysId;


}
