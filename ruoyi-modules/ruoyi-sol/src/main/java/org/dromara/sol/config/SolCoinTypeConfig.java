package org.dromara.sol.config;

import jakarta.annotation.PostConstruct;
import org.dromara.sol.enums.SolCoinType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;


/**
 * SolCoinType枚举配置初始化
 *
 * <AUTHOR>
 * @date 2025/5/9
 */
@Configuration
public class SolCoinTypeConfig {

    private final SolContractConfig contractConfig;

    @Autowired
    public SolCoinTypeConfig(SolContractConfig contractConfig) {
        this.contractConfig = contractConfig;
    }

    /**
     * 初始化SolCoinType枚举的合约地址配置
     */
    @PostConstruct
    public void init() {
        SolCoinType.setContractConfig(contractConfig);
    }
}
