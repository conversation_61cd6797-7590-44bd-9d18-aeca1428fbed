package org.dromara.sol.mapper;

import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.sol.domain.MetaSolanaTransaction;
import org.dromara.sol.domain.vo.MetaSolanaTransactionVo;

import java.util.List;

/**
 * Solana区块高度交易明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface MetaSolanaTransactionsMapper extends BaseMapperPlus<MetaSolanaTransaction, MetaSolanaTransactionVo> {
    @Select(value = "select * from meta_solana_transactions where type = 'collect' and  fromaddress=?1 order by `timestamp` desc limit 1 ")
    MetaSolanaTransaction findTime(String walletAddress);

    @Select(value = "SELECT fromaddress FROM meta_solana_transactions WHERE type = 'receive' AND issync = '1' and address=?1 and `timestamp`>?2 group by fromaddress")
    List<String> getList(String walletAddress, int timestamp);

    @Select(value = "SELECT fromaddress FROM meta_solana_transactions WHERE type = 'receive' AND issync = '1' and address=?1 group by fromaddress ")
    List<String> getList(String walletAddress);


    @Select(value = "SELECT fromaddress FROM meta_solana_transactions WHERE type = 'receive' AND issync = '0' and address=?1 ")
    List<MetaSolanaTransaction> getUnprocessedListByAddress(String walletAddress);

    @Select(value = "update meta_solana_transactions set issync=1 where address=?1 and type='receive' and issync = '0' ")
    void updateTransactionsS(String address);

    @Select(value = "update meta_solana_transactions set issync=1 where address=?1 and type='receive' and issync = '0' and blockheight < ?2 ")
    void updateTransactionsS(String address, int blockHeight);

    @Select(value = "update meta_solana_transactions set issync=?1 where txid=?2 ")
    void updateTransactions(int isSync, String txid);

    @Select(value = " select * from meta_solana_transactions where txid=?1 ")
    MetaSolanaTransaction findByTxid(String txid);


}
