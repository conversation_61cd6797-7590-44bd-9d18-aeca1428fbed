package org.dromara.sol.manager;

import cn.hutool.core.thread.ThreadUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bitcoinj.core.Base58;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.sol.config.SolRpcConfig;
import org.dromara.sol.config.SolWalletConfig;
import org.dromara.sol.domain.bo.MetaSolanaCstaddressinfoBo;
import org.dromara.sol.enums.SolCoinType;
import org.p2p.solanaj.core.Account;
import org.p2p.solanaj.core.PublicKey;
import org.p2p.solanaj.core.Transaction;
import org.p2p.solanaj.core.TransactionInstruction;
import org.p2p.solanaj.programs.AssociatedTokenProgram;
import org.p2p.solanaj.programs.SystemProgram;
import org.p2p.solanaj.programs.TokenProgram;
import org.p2p.solanaj.rpc.RpcClient;
import org.p2p.solanaj.rpc.RpcException;
import org.p2p.solanaj.rpc.types.*;
import org.p2p.solanaj.rpc.types.config.Commitment;
import org.p2p.solanaj.utils.TweetNaclFast;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.math.BigInteger;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/4/7 15:54
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class SolHttpManager {

    private final SolRpcConfig solRpcConfig;
    private final SolWalletConfig solWalletConfig;

    private static final String JSON_RPC = "2.0";
    private static final String ID = "1";

    /**
     * Solana交易参数Map常量
     */
    public static final HashMap<String, Object> TRANSACTION_PARAMS_MAP = new HashMap<>() {{
        put("encoding", "json");
        put("maxSupportedTransactionVersion", 0);
        put("transactionDetails", "full");
        put("rewards", false);
    }};

    /**
     * 内部类，用于表示批量转账中的单条指令
     */
    public static class BatchTransferInstruction {
        private final String destinationAccount;
        private final long amount; // lamports for SOL, smallest unit for tokens
        private final String tokenMintAddress; // null for SOL transfer
        private final Byte decimals; // null for SOL, otherwise token decimals for transferChecked

        // Constructor for SOL transfer
        public BatchTransferInstruction(String destinationAccount, long amount) {
            this.destinationAccount = destinationAccount;
            this.amount = amount;
            this.tokenMintAddress = null;
            this.decimals = null;
        }

        // Constructor for SPL Token transfer
        public BatchTransferInstruction(String destinationAccount, long amount, String tokenMintAddress, Byte decimals) {
            this.destinationAccount = destinationAccount;
            this.amount = amount;
            this.tokenMintAddress = tokenMintAddress;
            if (tokenMintAddress != null && decimals == null) {
                throw new IllegalArgumentException("Decimals must be provided for SPL token transfers.");
            }
            this.decimals = decimals;
        }

        public String getDestinationAccount() {
            return destinationAccount;
        }

        public long getAmount() {
            return amount;
        }

        public String getTokenMintAddress() {
            return tokenMintAddress;
        }

        public Byte getDecimals() {
            return decimals;
        }

        public boolean isSolTransfer() {
            return tokenMintAddress == null;
        }
    }

    /**
     * 内部类，用于表示代币转账指令
     */
    public static class TokenTransferInstruction {
        private final String tokenMintAddress; // 代币合约地址
        private final long amount; // 金额（最小单位）
        private final String destinationAccount; // 目标账户地址

        public TokenTransferInstruction(String tokenMintAddress, long amount, String destinationAccount) {
            this.tokenMintAddress = tokenMintAddress;
            this.amount = amount;
            this.destinationAccount = destinationAccount;
        }

        public String getTokenMintAddress() {
            return tokenMintAddress;
        }

        public long getAmount() {
            return amount;
        }

        public String getDestinationAccount() {
            return destinationAccount;
        }
    }

    /**
     * 获取交易信息
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public ConfirmedTransaction getTransaction(String signature) {
        try {
            ConfirmedTransaction transaction = solRpcConfig.getRpcClient().getApi().getTransaction(signature);
            if (transaction != null) {
                return transaction;
            } else {
                throw new BaseException("交易哈希错误:" + signature);
            }
        } catch (RpcException e) {
            log.error("获取交易失败:{}", e.getMessage());
            throw new BaseException("获取交易失败:" + e.getMessage());
        }
    }

    /**
     * 获取交易最终失败后的恢复方法
     */
    @Recover
    public ConfirmedTransaction recoverGetTransaction(BaseException e, String signature) {
        log.error("获取交易{}在多次尝试后失败：{}", signature, e.getMessage());
        throw new BaseException("获取交易经多次重试后失败:" + e.getMessage());
    }

    public HttpHeaders getHttpHeaders() {
        HttpHeaders standardHeaders = new HttpHeaders();
        standardHeaders.setContentType(MediaType.APPLICATION_JSON);
        standardHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        return standardHeaders;
    }

    /**
     * RPC请求
     * <p>
     * 添加重试机制，网络请求最多重试3次，初始退避1000ms，指数增长1.5倍
     */
    @Retryable(retryFor = {BaseException.class}, backoff = @Backoff(delay = 1000, multiplier = 1.5))
    public void getResponseEntity(String url, String method, Object innerParams) {
        HttpHeaders standardHeaders = getHttpHeaders();
        Map<String, Object> params = new HashMap<>();
        params.put("jsonrpc", JSON_RPC);
        params.put("method", method);
        params.put("params", innerParams);
        params.put("id", ID);
        HttpEntity<Map<String, Object>> httpEntity = new HttpEntity<>(params, standardHeaders);
        ResponseEntity<String> responseEntity = new RestTemplate().exchange(
            url,
            HttpMethod.POST, httpEntity, String.class);
        HttpStatusCode statusCode = responseEntity.getStatusCode();
        if (!statusCode.is2xxSuccessful()) {
            log.error("网络请求失败，URL: {}, 状态码: {}", url, statusCode);
            throw new BaseException("网络错误：请求quickNode失败");
        }
    }

    /**
     * 获取最新的slot
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public long getSlot() {
        RpcClient client = solRpcConfig.getRpcClient();
        try {
            return client.getApi().getSlot();
        } catch (RpcException e) {
            log.error("获取最新slot失败", e);
            throw new BaseException("获取slot失败:" + e.getMessage());
        }
    }

    /**
     * 获取这段区间的可用slot
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public List<Long> getAvailableSlot(long beginSlot, long endSlot) {
        RpcClient client = solRpcConfig.getRpcClient();
        try {
            return client.getApi().getBlocks(beginSlot, endSlot);
        } catch (RpcException e) {
            log.error("获取区间可用slot失败, beginSlot: {}, endSlot: {}", beginSlot, endSlot, e);
            throw new BaseException("获取可用slot失败:" + e.getMessage());
        }
    }


    /**
     * 获取指定slot数据
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public Block getSlotContent(long slot) {
        RpcClient client = solRpcConfig.getRpcClient();
        try {
            return client.getApi().getBlock((int) slot, TRANSACTION_PARAMS_MAP);
        } catch (RpcException e) {
            log.error("获取指定slot数据失败, slot: {}", slot, e);
            throw new BaseException("获取指定slot数据失败:" + e.getMessage());
        }
    }

    /**
     * 获取账户金额
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public BigInteger getBalance(String address) {
        RpcClient client = solRpcConfig.getRpcClient();
        PublicKey sourcePublicKey = new PublicKey(address);
        try {
            long balance = client.getApi().getBalance(sourcePublicKey);
            return BigInteger.valueOf(balance);
        } catch (RpcException e) {
            log.error("获取账户余额失败, 地址: {}", address, e);
            throw new BaseException("获取账户余额失败:" + e.getMessage());
        }
    }

    /**
     * 获取账户Token金额
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public TokenResultObjects.TokenAmountInfo getTokenBalance(String address, String tokenMintAddress) {
        PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);
        RpcClient client = solRpcConfig.getRpcClient();

        PublicKey sourcePublicKey = new PublicKey(address);
        PublicKey sourceTokenPublicKey;
        try {
            sourceTokenPublicKey = client.getApi().getTokenAccountsByOwner(sourcePublicKey, tokenMintPublicKey);
            return client.getApi().getTokenAccountBalance(sourceTokenPublicKey);
        } catch (RpcException e) {
            log.error("获取账户Token余额失败, 地址: {}, Token地址: {}", address, tokenMintAddress, e);
            throw new BaseException("获取账户Token余额失败:" + e.getMessage());
        }
    }

    /**
     * 验证交易签名
     * <p>
     * 添加重试机制，验证操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public boolean verify(String signature) {
        RpcClient client = solRpcConfig.getRpcClient();

        try {
            SignatureStatuses signatureStatuses = client.getApi().getSignatureStatuses(Collections.singletonList(signature), true);
            if (signatureStatuses.getValue().getFirst().getConfirmations() == null) {
                return true;
            } else {
                ThreadUtil.sleep(2000);
                signatureStatuses = client.getApi().getSignatureStatuses(Collections.singletonList(signature), true);
                return signatureStatuses.getValue().getFirst().getConfirmations() == null;
            }
        } catch (RpcException e) {
            log.error("验证交易签名失败, 签名: {}", signature, e);
            throw new BaseException("验证交易签名失败:" + e.getMessage());
        }
    }

    /**
     * 发起sol交易
     * <p>
     * 注意：转账操作不应该简单重试，因为可能导致重复转账。
     * 交易可能已经成功但返回过程中出现问题，简单重试会导致资金多次转移。
     */
    public String transfer(String sourcePrivateKey, String toAddress, long lamports) {
        RpcClient client = solRpcConfig.getRpcClient();
        byte[] decode;
        //拥有者账户
        if (sourcePrivateKey.endsWith("==")) {
            decode = Base64.getDecoder().decode(sourcePrivateKey);
        } else {
            decode = Base58.decode(sourcePrivateKey);
        }
        Account signer = new Account(decode);
        //目标账户
        PublicKey toPublicKey = new PublicKey(toAddress);

        //转账对象
        Transaction transaction = new Transaction();
        transaction.addInstruction(SystemProgram.transfer(signer.getPublicKey(), toPublicKey, lamports));

        try {
            return client.getApi().sendTransaction(transaction, signer);
        } catch (RpcException e) {
            log.error("发起SOL交易失败, 发送方: {}, 接收方: {}, 金额: {}", signer.getPublicKey(), toAddress, lamports, e);
            throw new BaseException("发起SOL交易失败:" + e.getMessage());
        }
    }

    /**
     * 创建token地址
     *
     * @param fundingPrivateKey  钱包私钥
     * @param destinationAccount 目标账户
     * @param tokenMintAddress   tokenMint地址
     * @return 交易签名
     * <p>
     * 注意：创建Token地址操作不应该简单重试，因为可能已经创建成功但返回失败。
     */
    public String createTokenAddress(String fundingPrivateKey, String destinationAccount, String tokenMintAddress) {
        try {
            RpcClient client = solRpcConfig.getRpcClient();
            byte[] decode;
            //拥有者账户
            if (fundingPrivateKey.endsWith("==")) {
                decode = Base64.getDecoder().decode(fundingPrivateKey);
            } else {
                decode = Base58.decode(fundingPrivateKey);
            }
            Account signer = new Account(decode);
            PublicKey sourcePublicKey = new PublicKey(signer.getPublicKeyBase58());
            //目标币种
            PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);
            //目标账户
            PublicKey destinationPublicKey = new PublicKey(destinationAccount);
            //转账对象
            Transaction transaction = new Transaction();
            TransactionInstruction createTokenInstruction = AssociatedTokenProgram.create(sourcePublicKey, destinationPublicKey, tokenMintPublicKey);
            transaction.addInstruction(createTokenInstruction);
            //api请求
            client.getApi().sendTransaction(transaction, signer);

            return createTokenInstruction.getKeys().get(1).getPublicKey().toString();

        } catch (RpcException e) {
            log.error("创建Token地址失败, 目标账户: {}, Token地址: {}", destinationAccount, tokenMintAddress, e);
            throw new BaseException("创建Token地址失败:" + e.getMessage());
        }
    }


    /**
     * 发起sol智能合约交易
     * <p>
     * 注意：Token转账操作不应该简单重试，因为可能导致重复转账。
     * 交易可能已经成功但返回过程中出现问题，简单重试会导致资金多次转移。
     */
    public String transferTokenBatch(String sourcePrivateKey, String destinationAccount, Map<String, Long> instructionMap) {
        try {
            RpcClient client = solRpcConfig.getRpcClient();
            byte[] decode;
            //拥有者账户
            if (sourcePrivateKey.endsWith("==")) {
                decode = Base64.getDecoder().decode(sourcePrivateKey);
            } else {
                decode = Base58.decode(sourcePrivateKey);
            }
            Account signer = new Account(decode);
            PublicKey sourcePublicKey = new PublicKey(signer.getPublicKeyBase58());
            //目标账户
            PublicKey destinationPublicKey = new PublicKey(destinationAccount);

            //转账对象
            Transaction transaction = new Transaction();
            instructionMap.forEach((tokenMintAddress, amount) -> {
                PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);
                try {
                    transaction.addInstruction(TokenProgram.transferChecked(
                        client.getApi().getTokenAccountsByOwner(sourcePublicKey, tokenMintPublicKey),
                        client.getApi().getTokenAccountsByOwner(destinationPublicKey, tokenMintPublicKey),
                        amount,
                        (byte) 6,
                        signer.getPublicKey(),
                        tokenMintPublicKey));
                } catch (RpcException e) {
                    log.error("构造Token交易失败, 发送方: {}, 接收方: {}, Token地址: {}, 金额: {}",
                        sourcePrivateKey.substring(0, 5) + "...", destinationAccount, tokenMintAddress, amount, e);
                }
            });

            return client.getApi().sendTransaction(transaction, signer);
        } catch (RpcException e) {
            log.error("发起Token交易失败, 发送方: {}, 接收方: {}", sourcePrivateKey.substring(0, 5) + "...", destinationAccount, e);
            throw new BaseException("发起Token交易失败:" + e.getMessage());
        }
    }

    /**
     * 解析账户地址
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public String parseAccountToSolAddress(String splTokenAddress) {
        PublicKey splTokenPublicKey = new PublicKey(splTokenAddress);
        RpcClient client = solRpcConfig.getRpcClient();

        HashMap<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("encoding", "base64");
        AccountInfo accountInfo;
        try {
            accountInfo = client.getApi().getAccountInfo(splTokenPublicKey, paramsMap);
        } catch (RpcException e) {
            log.error("获取SPL Token账户信息失败, 地址: {}", splTokenAddress, e);
            throw new BaseException("获取SPL Token账户信息失败:" + e.getMessage());
        }
        //解码
        byte[] data = Base64.getDecoder().decode(accountInfo.getValue().getData().getFirst());
        // 3. 取32~63字节（owner）
        byte[] owner = Arrays.copyOfRange(data, 32, 64);
        // 4. base58编码，得到SOL主账户地址
        return Base58.encode(owner);
    }

    /**
     * 获取指定地址的最近几次交易记录
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public List<SignatureInformation> getSignaturesForAddress(String address, int limit) {
        RpcClient client = solRpcConfig.getRpcClient();
        try {
            List<SignatureInformation> signaturesForAddress = client.getApi().getSignaturesForAddress(new PublicKey(address), limit, Commitment.FINALIZED);
            signaturesForAddress.removeIf(item -> item != null && item.getErr() != null);
            return signaturesForAddress;
        } catch (RpcException e) {
            log.error("获取地址交易记录失败, 地址: {}, 限制: {}", address, limit, e);
            throw new BaseException("获取地址交易记录失败:" + e.getMessage());
        }
    }

    public void generateAccount(MetaSolanaCstaddressinfoBo bo) {
        try {
            // 生成新的 Ed25519 密钥对
            TweetNaclFast.Signature.KeyPair keyPair = TweetNaclFast.Signature.keyPair();
            // 获取私钥和公钥
            byte[] secretKey = keyPair.getSecretKey(); // 64字节
            byte[] publicKey = keyPair.getPublicKey(); // 32字节
            // Solana 钱包地址就是公钥的 base58 编码
            String pubKeyBase58 = Base58.encode(publicKey);

            bo.setCstAddress(pubKeyBase58);
            bo.setCstPrivate(java.util.Base64.getEncoder().encodeToString(secretKey));

            //激活账号（注意激活账号失败处理，手动激活还要记得修改表）
            String tokenUsdtMint = createTokenAddress(solWalletConfig.getFundingPrivateKey(), bo.getCstAddress(), SolCoinType.USDT.getContractAddress());
            bo.setCstUsdtAddress(tokenUsdtMint);
            String tokenUsdcMint = createTokenAddress(solWalletConfig.getFundingPrivateKey(), bo.getCstAddress(), SolCoinType.USDC.getContractAddress());
            bo.setCstUsdcAddress(tokenUsdcMint);


        } catch (Exception e) {
            log.error("生成Solana账户失败", e);
            throw new BaseException("生成Solana账户失败:" + e.getMessage());
        }
    }

    /**
     * 获取交易信息
     * <p>
     * 添加重试机制，查询操作最多重试3次，初始退避500ms，指数增长2倍
     */
    @Retryable(retryFor = {BaseException.class, RpcException.class}, backoff = @Backoff(delay = 500, multiplier = 2))
    public ConfirmedTransaction getTransactionInfo(String hash) {
        RpcClient client = solRpcConfig.getRpcClient();
        try {
            return client.getApi().getTransaction(hash);
        } catch (RpcException e) {
            log.error("获取交易信息失败, 交易哈希: {}", hash, e);
            throw new BaseException("获取交易信息失败:" + e.getMessage());
        }
    }

    /**
     * 发起sol智能合约交易
     * <p>
     * 注意：Token转账操作不应该简单重试，因为可能导致重复转账。
     * 交易可能已经成功但返回过程中出现问题，简单重试会导致资金多次转移。
     */
    public String transferToken(String sourcePrivateKey, String destinationAccount, long amount, String tokenMintAddress) {
        try {
            PublicKey tokenMintPublicKey = new PublicKey(tokenMintAddress);
            RpcClient client = solRpcConfig.getRpcClient();
            byte[] decode;
            //拥有者账户
            if (sourcePrivateKey.endsWith("==")) {
                decode = Base64.getDecoder().decode(sourcePrivateKey);
            } else {
                decode = Base58.decode(sourcePrivateKey);
            }
            Account signer = new Account(decode);
            PublicKey sourcePublicKey = new PublicKey(signer.getPublicKeyBase58());
            PublicKey sourceTokenPublicKey = client.getApi().getTokenAccountsByOwner(sourcePublicKey, tokenMintPublicKey);

            //目标账户
            PublicKey destinationPublicKey = new PublicKey(destinationAccount);

            //目标mint
            PublicKey destinationTokenPublicKey = client.getApi().getTokenAccountsByOwner(destinationPublicKey, tokenMintPublicKey);

            //转账对象
            Transaction transaction = new Transaction();
            transaction.addInstruction(TokenProgram.transferChecked(
                sourceTokenPublicKey,
                destinationTokenPublicKey,
                amount,
                (byte) 6,
                signer.getPublicKey(),
                tokenMintPublicKey));
            return client.getApi().sendTransaction(transaction, signer);
        } catch (RpcException e) {
            log.error("发起Token交易失败, 发送方: {}, 接收方: {}, Token地址: {}, 金额: {}",
                sourcePrivateKey.substring(0, 5) + "...", destinationAccount, tokenMintAddress, amount, e);
            throw new BaseException("发起Token交易失败:" + e.getMessage());
        }
    }

    /**
     * 发起批量代币交易（支持多笔交易，支持不同代币和不同目标地址）
     * <p>
     * 注意：批量转账操作不应该简单重试，因为可能导致部分操作重复。
     * 交易可能已经成功但返回过程中出现问题，简单重试会导致资金多次转移。
     *
     * @param sourcePrivateKey 源账户私钥
     * @param instructions     转账指令列表（每个指令包含代币地址、金额和目标账户）
     * @return 交易签名
     */
    public String transferTokenBatchList(String sourcePrivateKey, List<TokenTransferInstruction> instructions) {
        try {
            RpcClient client = solRpcConfig.getRpcClient();
            byte[] decode;
            //拥有者账户
            if (sourcePrivateKey.endsWith("==")) {
                decode = Base64.getDecoder().decode(sourcePrivateKey);
            } else {
                decode = Base58.decode(sourcePrivateKey);
            }
            Account signer = new Account(decode);
            PublicKey sourcePublicKey = new PublicKey(signer.getPublicKeyBase58());

            //转账对象
            Transaction transaction = new Transaction();
            for (TokenTransferInstruction instruction : instructions) {
                PublicKey tokenMintPublicKey = new PublicKey(instruction.getTokenMintAddress());
                PublicKey destinationPublicKey = new PublicKey(instruction.getDestinationAccount());

                try {
                    transaction.addInstruction(TokenProgram.transferChecked(
                        client.getApi().getTokenAccountsByOwner(sourcePublicKey, tokenMintPublicKey),
                        client.getApi().getTokenAccountsByOwner(destinationPublicKey, tokenMintPublicKey),
                        instruction.getAmount(),
                        (byte) 6,
                        signer.getPublicKey(),
                        tokenMintPublicKey));
                } catch (RpcException e) {
                    log.error("构造Token交易失败, 发送方: {}, 接收方: {}, Token地址: {}, 金额: {}",
                        sourcePrivateKey.substring(0, 5) + "...", instruction.getDestinationAccount(),
                        instruction.getTokenMintAddress(), instruction.getAmount(), e);
                }
            }

            return client.getApi().sendTransaction(transaction, signer);
        } catch (RpcException e) {
            log.error("发起批量Token交易失败, 发送方: {}, 错误: {}",
                sourcePrivateKey.substring(0, 5) + "...", e.getMessage(), e);
            throw new BaseException("发起批量Token交易失败:" + e.getMessage());
        }
    }

    /**
     * 为兼容性保留单一目标地址的批量转账方法
     */
    public String transferTokenBatchList(String sourcePrivateKey, String destinationAccount, List<TokenTransferInstruction> instructions) {
        // 将旧格式转换为新格式
        List<TokenTransferInstruction> newInstructions = new ArrayList<>();
        for (TokenTransferInstruction instruction : instructions) {
            newInstructions.add(new TokenTransferInstruction(
                instruction.getTokenMintAddress(),
                instruction.getAmount(),
                destinationAccount));
        }
        return transferTokenBatchList(sourcePrivateKey, newInstructions);
    }
}
