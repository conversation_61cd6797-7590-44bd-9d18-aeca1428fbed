package org.dromara.sol.mq;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.sol.config.SolWalletConfig;
import org.dromara.sol.dto.MetaMainAddress;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NormalRabbitProducer {

    private final RabbitTemplate rabbitTemplate;
    private final SolWalletConfig walletConfig;

//    public void send(String message) {
//        rabbitTemplate.convertAndSend(RabbitConfig.EXCHANGE_NAME, RabbitConfig.ROUTING_KEY, message);
//        log.info("【生产者】Message send: " + message);
//    }


    public void sendMonitor(MqMonitorDto message) {
        //超过多少钱入账，需要通知管理员检查
        BigDecimal alarmThreshold = null;
        //查询主钱包配置的告警阈值
        MetaMainAddress mainAddress = walletConfig.getMainAddressBySysId(message.getSysId());
        if (mainAddress != null) {
            alarmThreshold = mainAddress.getAlarmThreshold();
        }
        if (alarmThreshold == null) {
            MailUtils.sendText(
                "<EMAIL>",
                "入账失败的信息提醒",
                String.format("入账失败，没有配置'notificationThreshold'。message: %s", message.getMessage()));
            throw new BaseException("入账失败，没有配置'notificationThreshold'");
        } else {
            if (message.getAmount().compareTo(alarmThreshold) >= 0) {
                send(message, RabbitConfig.EXCHANGE_NAME, RabbitConfig.ROUTING_KEY);
            }
        }
    }


    /**
     * 通用 MQ 消息发送方法，支持任意类型对象
     *
     * @param payload    消息体（对象、Map、List、String 等）
     * @param exchange   交换机名称
     * @param routingKey 路由键
     */
    public void send(Object payload, String exchange, String routingKey) {
        try {
            MessageConverter converter = rabbitTemplate.getMessageConverter();
            MessageProperties messageProperties = new MessageProperties();
            Message message = converter.toMessage(payload, messageProperties);

            // 生成唯一 ID，便于 confirm 回调追踪
            String messageId = UUID.randomUUID().toString();
            CustomCorrelationData correlationData =
                new CustomCorrelationData(messageId, message, exchange, routingKey);

            log.info("发送 MQ 消息：exchange={}, routingKey={}, messageId={}, payload={}",
                exchange, routingKey, messageId, payload);

            rabbitTemplate.send(exchange, routingKey, message, correlationData);
        } catch (Exception e) {
            log.error("发送 MQ 消息失败：payload={}, error={}", payload, e.getMessage(), e);
        }
    }

    /**
     * 自定义 CorrelationData
     */
    @Getter
    public static class CustomCorrelationData extends CorrelationData {
        private final Message message;
        private final String exchange;
        private final String routingKey;

        public CustomCorrelationData(String id, Message message, String exchange, String routingKey) {
            super(id);
            this.message = message;
            this.exchange = exchange;
            this.routingKey = routingKey;
        }

    }


}
