package org.dromara.sol.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;

/**
 * Solana监控配置
 * 统一管理WebSocket监控相关配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "solana.monitor")
@EnableRetry
public class SolanaMonitorConfig {

    /**
     * WebSocket连接超时时间（秒）
     */
    private int connectionTimeout = 30;

    /**
     * 最大重连次数
     */
    private int maxReconnectAttempts = 5;

    /**
     * 重连间隔（秒）
     */
    private int reconnectInterval = 10;

    /**
     * 订阅限流：每秒最大请求数
     */
    private int subscribeRateLimit = 10;

    /**
     * 是否启用交易补偿
     */
    private boolean enableCompensation = true;

    /**
     * 补偿检查间隔（分钟）
     */
    private int compensationInterval = 60;

    /**
     * 是否启用自动重连
     */
    private boolean enableAutoReconnect = true;

    /**
     * 心跳间隔（秒）
     */
    private int heartbeatInterval = 30;

    /**
     * 防重复订阅时间阈值（分钟）
     * 在此时间内不会进行重复的全量订阅
     */
    private int duplicateSubscriptionThresholdMinutes = 10;
}
