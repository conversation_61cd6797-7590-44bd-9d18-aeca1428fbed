package org.dromara.sol.exception;

/**
 * Solana WebSocket连接异常
 * 用于处理WebSocket连接创建和初始化过程中的异常
 */
public class SolanaWebSocketConnectionException extends SolanaWebSocketException {

    private static final long serialVersionUID = 1L;

    public SolanaWebSocketConnectionException(String message) {
        super(message);
    }

    public SolanaWebSocketConnectionException(String message, Throwable cause) {
        super(message, cause);
    }

    public SolanaWebSocketConnectionException(Throwable cause) {
        super("Solana WebSocket连接初始化失败: " + cause.getMessage(), cause);
    }
}
