package org.dromara.sol.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 交易事件
 * 用于在解析出交易信息后发布事件，解耦交易解析和处理
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@Getter
public class TransactionEvent extends ApplicationEvent {

    /**
     * 交易签名
     */
    private final String signature;

    /**
     * 关联地址（可选）
     */
    private final String address;

    /**
     * 创建交易事件
     *
     * @param source    事件源
     * @param signature 交易签名
     */
    public TransactionEvent(Object source, String signature) {
        this(source, signature, null);
    }

    /**
     * 创建带地址信息的交易事件
     *
     * @param source    事件源
     * @param signature 交易签名
     * @param address   关联地址
     */
    public TransactionEvent(Object source, String signature, String address) {
        super(source);
        this.signature = signature;
        this.address = address;
    }

    /**
     * 判断是否有关联地址
     *
     * @return 是否有关联地址
     */
    public boolean hasAddress() {
        return address != null && !address.isEmpty();
    }
}
