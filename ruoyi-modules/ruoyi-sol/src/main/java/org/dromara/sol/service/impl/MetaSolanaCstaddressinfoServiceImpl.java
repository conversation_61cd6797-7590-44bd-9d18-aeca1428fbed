package org.dromara.sol.service.impl;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.sol.domain.MetaSolanaCstaddressinfo;
import org.dromara.sol.domain.bo.MetaSolanaCstaddressinfoBo;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.event.WalletMonitorEvent;
import org.dromara.sol.manager.SolHttpManager;
import org.dromara.sol.mapper.MetaSolanaCstaddressinfoMapper;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.p2p.solanaj.rpc.types.SignatureInformation;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * Solana客户热钱包信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MetaSolanaCstaddressinfoServiceImpl implements IMetaSolanaCstaddressinfoService {

    private final SolHttpManager solHttpManager;
    private final MetaSolanaCstaddressinfoMapper baseMapper;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 查询Solana客户热钱包信息
     *
     * @param id 主键
     * @return Solana客户热钱包信息
     */
    @Override
    public MetaSolanaCstaddressinfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 按照地址查询钱包信息
     */
    @Override
    public MetaSolanaCstaddressinfo getOneByAddress(String address) {
        return baseMapper.selectOne(new LambdaQueryWrapper<MetaSolanaCstaddressinfo>().eq(MetaSolanaCstaddressinfo::getCstAddress, address));
    }

    /**
     * 查询全部
     */
    @Override
    @Cacheable(value = "solanaCstAddressInfo", key = "'all'", unless = "#result == null")
    public List<MetaSolanaCstaddressinfoVo> queryAll() {
        return baseMapper.selectVoList(null);
    }

    /**
     * 查询全部
     */
    @Override
//    @Cacheable(value = "solanaCstAddressInfo", key = "'allHashSet'", unless = "#result == null")
    public HashSet<String> queryAllAddressSet() {
        HashSet<String> addressHashSet = new HashSet<>();
        List<MetaSolanaCstaddressinfoVo> metaSolanaCstaddressinfoVos = baseMapper.selectVoList(null);
        metaSolanaCstaddressinfoVos.forEach(item -> {
            addressHashSet.add(item.getCstAddress());
            if (StringUtils.isNotBlank(item.getCstUsdtAddress())) {
                addressHashSet.add(item.getCstUsdtAddress());
            }
            if (StringUtils.isNotBlank(item.getCstUsdcAddress())) {
                addressHashSet.add(item.getCstUsdcAddress());
            }
        });
        return addressHashSet;
    }


    /**
     * 分页查询Solana客户热钱包信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return Solana客户热钱包信息分页列表
     */
    @Override
    public TableDataInfo<MetaSolanaCstaddressinfoVo> queryPageList(MetaSolanaCstaddressinfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MetaSolanaCstaddressinfo> lqw = buildQueryWrapper(bo);
        Page<MetaSolanaCstaddressinfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的Solana客户热钱包信息列表
     *
     * @param bo 查询条件
     * @return Solana客户热钱包信息列表
     */
    @Override
    public List<MetaSolanaCstaddressinfoVo> queryList(MetaSolanaCstaddressinfoBo bo) {
        LambdaQueryWrapper<MetaSolanaCstaddressinfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MetaSolanaCstaddressinfo> buildQueryWrapper(MetaSolanaCstaddressinfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MetaSolanaCstaddressinfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(MetaSolanaCstaddressinfo::getId);
        lqw.eq(bo.getCstId() != null, MetaSolanaCstaddressinfo::getCstId, bo.getCstId());
        lqw.eq(StringUtils.isNotBlank(bo.getCstAddress()), MetaSolanaCstaddressinfo::getCstAddress, bo.getCstAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getCstPrivate()), MetaSolanaCstaddressinfo::getCstPrivate, bo.getCstPrivate());
        lqw.eq(StringUtils.isNotBlank(bo.getCstUsdtAddress()), MetaSolanaCstaddressinfo::getCstUsdtAddress, bo.getCstUsdtAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getCstUsdcAddress()), MetaSolanaCstaddressinfo::getCstUsdcAddress, bo.getCstUsdcAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getSysId()), MetaSolanaCstaddressinfo::getSysId, bo.getSysId());
        return lqw;
    }

    @Override
    public MetaSolanaCstaddressinfoVo createAccount(MetaSolanaCstaddressinfoBo bo) {
        // 先检查用户是否已有钱包
        LambdaQueryWrapper<MetaSolanaCstaddressinfo> eq = Wrappers.<MetaSolanaCstaddressinfo>lambdaQuery()
            .eq(MetaSolanaCstaddressinfo::getCstId, bo.getCstId());
        MetaSolanaCstaddressinfoVo metaSolanaCstaddressinfoVo = baseMapper.selectVoOne(eq);
        if (metaSolanaCstaddressinfoVo != null) {
            return metaSolanaCstaddressinfoVo;
        }

        // 尝试创建钱包，最多尝试3次
        for (int attempts = 0; attempts < 3; attempts++) {
            // 生成新钱包
            solHttpManager.generateAccount(bo);

            // 检查钱包是否已被使用
            if (!hasTransactionHistory(bo.getCstAddress())) {
                // 钱包未被使用，保存并返回
                log.info("成功为用户{}创建未使用的钱包地址: {}", bo.getCstId(), bo.getCstAddress());
                TenantHelper.setDynamic("000000",true);
                SpringUtils.getBean(this.getClass()).insertByBo(bo);
                return baseMapper.selectVoById(bo.getId());
            }

            log.warn("生成的钱包地址{}已被使用，正在重新生成...", bo.getCstAddress());
        }

        // 如果多次尝试后仍无法创建未使用的钱包，抛出异常
        throw new BaseException("无法生成未使用的钱包，请稍后重试");
    }

    /**
     * 新增Solana客户热钱包信息
     *
     * @param bo Solana客户热钱包信息
     * @return 是否新增成功
     */
    @Override
    @CacheEvict(value = "solanaCstAddressInfo", key = "'all'")
    @Lock4j(keys = "#bo.cstId")
    public Boolean insertByBo(MetaSolanaCstaddressinfoBo bo) {
        MetaSolanaCstaddressinfo add = MapstructUtils.convert(bo, MetaSolanaCstaddressinfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            assert add != null;
            bo.setId(add.getId());
            eventPublisher.publishEvent(new WalletMonitorEvent(this, baseMapper.selectVoById(add.getId())));
        }
        return flag;
    }

    /**
     * 修改Solana客户热钱包信息
     *
     * @param bo Solana客户热钱包信息
     * @return 是否修改成功
     */
    @Override
    @CacheEvict(value = "solanaCstAddressInfo", key = "'all'")
    public Boolean updateByBo(MetaSolanaCstaddressinfoBo bo) {
        MetaSolanaCstaddressinfo update = MapstructUtils.convert(bo, MetaSolanaCstaddressinfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MetaSolanaCstaddressinfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除Solana客户热钱包信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @CacheEvict(value = "solanaCstAddressInfo", key = "'all'")
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 检查钱包地址是否已被使用（是否有交易历史）
     *
     * @param walletAddress 钱包地址
     * @return 如果钱包有交易历史则返回true，否则返回false
     */
    @Override
    public boolean hasTransactionHistory(String walletAddress) {
        try {
            // 获取钱包的最近1条交易签名信息
            List<SignatureInformation> signatures = solHttpManager.getSignaturesForAddress(walletAddress, 1);

            // 如果有交易记录，则说明钱包已被使用
            return !signatures.isEmpty();
        } catch (Exception e) {
            log.error("检查钱包交易历史失败, 地址: {}", walletAddress, e);
            // 当查询异常时，保守起见，认为钱包可能已被使用
            return true;
        }
    }

}
