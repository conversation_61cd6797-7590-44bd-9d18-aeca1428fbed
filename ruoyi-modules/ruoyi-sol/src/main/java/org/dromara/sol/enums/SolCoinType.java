package org.dromara.sol.enums;

import lombok.Getter;
import org.dromara.sol.config.SolContractConfig;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/5/8 16:28
 **/
@Getter
public enum SolCoinType {
    SOL("SOL", "SOL"),
    USDT("USDT", "USDT"),
    USDC("USDC", "USDC");

    private final String code;
    private final String description;
    private String contractAddress;

    // 静态配置实例，用于在枚举实例化时注入
    private static SolContractConfig contractConfig;

    SolCoinType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取合约地址
     *
     * @return 合约地址
     */
    public String getContractAddress() {
        if (this == SOL) return "SOL"; // Native SOL has no mint address, return special value
        if (contractAddress == null && contractConfig != null) {
            // 从配置中获取合约地址
            contractAddress = contractConfig.getContractAddress(code.toLowerCase());
        }
        return contractAddress;
    }

    /**
     * 获取代币小数位数
     *
     * @return 小数位数
     */
    public int getDecimals() {
        if (this == SOL) {
            return 9; // Solana native token (SOL) has 9 decimals
        }
        if (contractConfig != null) {
            // 从配置中获取小数位数
            return contractConfig.getContractDecimals(this.code.toLowerCase());
        }
        // Fallback if contractConfig is not injected or decimals not found for the code
        // This fallback should ideally not be hit if configurations are correct.
        return 6; // Default to 6 as a general fallback for SPL tokens
    }

    /**
     * 注入配置
     *
     * @param config 合约配置
     */
    public static void setContractConfig(SolContractConfig config) {
        SolCoinType.contractConfig = config;

        // 初始化所有枚举值的合约地址
        for (SolCoinType type : values()) {
            type.contractAddress = config.getContractAddress(type.code.toLowerCase());
        }
    }

    /**
     * 根据合约地址查找对应的代币类型
     *
     * @param contractAddress 合约地址
     * @return 对应的代币类型，如果未找到则返回null
     */
    public static SolCoinType fromContractAddress(String contractAddress) {
        if (contractAddress == null || contractAddress.isEmpty() || contractAddress.equalsIgnoreCase("SOL")) {
            // Handle native SOL or empty/null contractAddress
            // If 'SOL' is passed, it should probably map to SolCoinType.SOL
            if (contractAddress != null && contractAddress.equalsIgnoreCase("SOL")) return SOL;
            return null;
        }

        for (SolCoinType type : values()) {
            if (Objects.equals(contractAddress, type.getContractAddress())) {
                return type;
            }
        }

        return null;
    }
}
