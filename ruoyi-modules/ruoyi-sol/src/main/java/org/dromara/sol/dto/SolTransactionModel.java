package org.dromara.sol.dto;

import lombok.Getter;
import org.p2p.solanaj.rpc.types.Block;
import org.p2p.solanaj.rpc.types.ConfirmedTransaction;

/**
 * Solana交易模型
 *
 * <AUTHOR>
 * @date 2025/4/13 21:55
 **/
@Getter
public class SolTransactionModel {
    /**
     * 区块信息
     */
    private Block solBlock;

    /**
     * 交易对象
     */
    private ConfirmedTransaction transactionObject;

    public static SolTransactionModelBuilder builder() {
        return new SolTransactionModelBuilder();
    }

    public static class SolTransactionModelBuilder {
        private SolTransactionModel solTransactionModel = new SolTransactionModel();

        public SolTransactionModelBuilder setSolBlock(Block solBlock) {
            solTransactionModel.solBlock = solBlock;
            return this;
        }

        public SolTransactionModelBuilder setTransactionObject(ConfirmedTransaction transactionObject) {
            solTransactionModel.transactionObject = transactionObject;
            return this;
        }

        public SolTransactionModel build() {
            return solTransactionModel;
        }
    }
}
