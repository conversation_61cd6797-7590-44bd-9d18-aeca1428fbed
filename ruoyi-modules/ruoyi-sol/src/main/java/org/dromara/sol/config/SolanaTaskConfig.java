package org.dromara.sol.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Solana相关定时任务配置
 * 可以通过配置文件控制是否启用定时任务
 *
 * <AUTHOR>
 */
@Configuration
@EnableScheduling
@ConditionalOnProperty(prefix = "solana", name = "retry-task-enabled", havingValue = "true", matchIfMissing = true)
public class SolanaTaskConfig {
    // 通过配置控制定时任务启用
    // 可以在配置文件中添加 solana.retry-task-enabled=false 来禁用定时任务
}
