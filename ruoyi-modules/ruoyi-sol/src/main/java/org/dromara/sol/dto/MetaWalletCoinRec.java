package org.dromara.sol.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigInteger;
import java.util.Date;

/**
 * 多链用户钱包代币余额记录表
 *
 * <AUTHOR>
 * @date 2025/6/3 15:30
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetaWalletCoinRec {

    /**
     * ID
     */
    private Long id;

    /**
     * 钱包地址
     */
    private String walletAddress;

    /**
     * 代币合约地址 (原生代币固定值，如TRX为"_TRX_NATIVE_")
     */
    private String tokenAddress;

    /**
     * 代币符号 (例如: TRX, USDT, USDC)
     */
    private String tokenSymbol;

    /**
     * 原始余额 (链上最小单位，使用BigInteger存储)
     */
    private BigInteger rawBalance;

    /**
     * 代币小数位数
     */
    private Integer decimals;

    /**
     * 链类型 (例如: TRON, ETH, BSC)
     */
    private String chainType;

    /**
     * 创建部门
     */
    private Long createDept;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间 / 余额快照记录时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;
}
