package org.dromara.sol.service;

import org.dromara.sol.domain.MetaSolanaCstaddressinfo;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.domain.bo.MetaSolanaCstaddressinfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.cache.annotation.Cacheable;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;

/**
 * Solana客户热钱包信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IMetaSolanaCstaddressinfoService {

    /**
     * 查询Solana客户热钱包信息
     *
     * @param id 主键
     * @return Solana客户热钱包信息
     */
    MetaSolanaCstaddressinfoVo queryById(Long id);

    MetaSolanaCstaddressinfo getOneByAddress(String address);

    List<MetaSolanaCstaddressinfoVo> queryAll();

    HashSet<String> queryAllAddressSet();

    /**
     * 分页查询Solana客户热钱包信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return Solana客户热钱包信息分页列表
     */
    TableDataInfo<MetaSolanaCstaddressinfoVo> queryPageList(MetaSolanaCstaddressinfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的Solana客户热钱包信息列表
     *
     * @param bo 查询条件
     * @return Solana客户热钱包信息列表
     */
    List<MetaSolanaCstaddressinfoVo> queryList(MetaSolanaCstaddressinfoBo bo);

    MetaSolanaCstaddressinfoVo createAccount(MetaSolanaCstaddressinfoBo bo);

    /**
     * 新增Solana客户热钱包信息
     *
     * @param bo Solana客户热钱包信息
     * @return 是否新增成功
     */
    Boolean insertByBo(MetaSolanaCstaddressinfoBo bo);

    /**
     * 修改Solana客户热钱包信息
     *
     * @param bo Solana客户热钱包信息
     * @return 是否修改成功
     */
    Boolean updateByBo(MetaSolanaCstaddressinfoBo bo);

    /**
     * 校验并批量删除Solana客户热钱包信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    boolean hasTransactionHistory(String walletAddress);
}
