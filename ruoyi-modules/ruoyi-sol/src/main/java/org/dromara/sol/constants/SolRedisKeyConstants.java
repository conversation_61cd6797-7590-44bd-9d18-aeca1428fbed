package org.dromara.sol.constants;

/**
 * Solana监控地址相关的Redis键常量
 *
 * <AUTHOR>
 */
public class SolRedisKeyConstants {

    /**
     * Solana监控地址哈希表，记录所有被监控地址的状态
     * 结构: Hash - {address: status}
     */
    public static final String SOL_MONITOR_ADDRESSES = "solana:monitor:addresses";

    /**
     * Solana连接失败需要重试的地址队列
     * 结构: List - [address1, address2, ...]
     */
    public static final String SOL_MONITOR_FAILED_QUEUE = "solana:monitor:failed_queue";

    /**
     * Solana地址详情信息
     * 结构: Hash - {id, address, usdtAddress, usdcAddress, status, lastUpdateTime}
     */
    public static final String SOL_MONITOR_ADDRESS_DETAIL_PREFIX = "solana:monitor:address:";

    /**
     * Solana WebSocket订阅ID与地址的映射关系
     * 结构: Hash - {subscriptionId: address}
     */
    public static final String SOL_SUBSCRIPTION_MAPPING = "solana:monitor:subscription_mapping";

    /**
     * 状态：待连接
     */
    public static final String STATUS_PENDING = "PENDING";

    /**
     * 状态：监控中
     */
    public static final String STATUS_MONITORING = "MONITORING";

    /**
     * 状态：连接失败
     */
    public static final String STATUS_FAILED = "FAILED";
}
