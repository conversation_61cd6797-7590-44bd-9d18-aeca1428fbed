package org.dromara.sol.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sol.domain.bo.MetaSolanaTransactionBo;
import org.dromara.sol.domain.vo.MetaSolanaTransactionVo;

import java.util.Collection;
import java.util.List;

/**
 * Solana区块高度交易明细Service接口
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
public interface IMetaSolanaTransactionsService {

    /**
     * 查询Solana区块高度交易明细
     *
     * @param id 主键
     * @return Solana区块高度交易明细
     */
    MetaSolanaTransactionVo queryById(Long id);

    Boolean isExist(String signature);

    /**
     * 分页查询Solana区块高度交易明细列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return Solana区块高度交易明细分页列表
     */
    TableDataInfo<MetaSolanaTransactionVo> queryPageList(MetaSolanaTransactionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的Solana区块高度交易明细列表
     *
     * @param bo 查询条件
     * @return Solana区块高度交易明细列表
     */
    List<MetaSolanaTransactionVo> queryList(MetaSolanaTransactionBo bo);

    /**
     * 新增Solana区块高度交易明细
     *
     * @param bo Solana区块高度交易明细
     * @return 是否新增成功
     */
    Boolean insertByBo(MetaSolanaTransactionBo bo);

    /**
     * 修改Solana区块高度交易明细
     *
     * @param bo Solana区块高度交易明细
     * @return 是否修改成功
     */
    Boolean updateByBo(MetaSolanaTransactionBo bo);

    /**
     * 校验并批量删除Solana区块高度交易明细信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
