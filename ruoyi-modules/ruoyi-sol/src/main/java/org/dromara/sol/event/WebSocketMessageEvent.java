package org.dromara.sol.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * WebSocket消息事件
 * 用于在接收到WebSocket消息时发布事件，解耦消息接收和处理
 *
 * <AUTHOR>
 * @date 2025-05-01
 */
@Getter
public class WebSocketMessageEvent extends ApplicationEvent {

    /**
     * WebSocket消息内容
     */
    private final String message;

    /**
     * 创建WebSocket消息事件
     *
     * @param source  事件源
     * @param message WebSocket消息内容
     */
    public WebSocketMessageEvent(Object source, String message) {
        super(source);
        this.message = message;
    }
}
