package org.dromara.sol.mq;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/4/10 14:06
 * <p>
 * 余额变化 2,000 USDT ［xcode］
 * 交易时间：2025-04-11 15:11:12
 * 所属公链：Tron
 * 监听地址：
 * THiKsGYn4cVHzaRU7kzHfBfEFcN57b456P ［xcode］
 * 来源地址：
 * TZECzuFfh6kYYJgFEduo8YZbLC9YbJ3JPz
 * 交易类型：转入
 * 交易金额：2,000 USDT
 * 均TRX余额：240.082024 TRX
 * USDT余额：2,299.892776 USDT
 * </p>
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MqMonitorDto implements Serializable {
    // 确保你的所有字段也是可序列化的，或者标记为 transient
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 渠道
     */
    private String channel;

    /**
     * 交易时间
     */
    private long transactionTime;
    /**
     * 所属公链
     */
    private String chain;
    /**
     * 监听地址
     */
    private String userAddress;
    /**
     * 关联地址
     */
    private String otherAddress;
    /**
     * 交易类型
     */
    private String type;
    /**
     * 交易金额
     */
    private BigDecimal amount;
    /**
     * 交易币种
     */
    private String amountCoin;
    /**
     * 余额
     */
    private BigDecimal balance;
    /**
     * 余额币种
     */
    private String balanceCoin;
    /**
     * Token余额
     */
    private BigDecimal tokenBalance;
    /**
     * Token币种
     */
    private String tokenBalanceCoin;

    /**
     * 其他信息
     */
    private String message;
    /**
     * 多租户
     */
    private String sysId;

    public MqMonitorDto(long transactionTime, String chain, String userAddress, String otherAddress, String type, BigDecimal amount, String amountCoin, BigDecimal balance, String balanceCoin, BigDecimal tokenBalance, String tokenBalanceCoin, String sysId) {
        this.channel = "wallet";
        this.transactionTime = transactionTime;
        this.chain = chain;
        this.userAddress = userAddress;
        this.otherAddress = otherAddress;
        this.type = type;
        this.amount = amount;
        this.amountCoin = amountCoin;
        this.balance = balance;
        this.balanceCoin = balanceCoin;
        this.tokenBalance = tokenBalance;
        this.tokenBalanceCoin = tokenBalanceCoin;
        this.sysId = sysId;
    }
}

