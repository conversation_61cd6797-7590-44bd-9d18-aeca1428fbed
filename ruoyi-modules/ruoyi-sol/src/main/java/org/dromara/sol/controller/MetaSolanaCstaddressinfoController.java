package org.dromara.sol.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.lock.annotation.Lock4j;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sol.domain.bo.MetaSolanaCstaddressinfoBo;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Solana客户热钱包信息
 *
 * <AUTHOR>
 * @date 2025-04-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sol/solanaCstaddressinfo")
public class MetaSolanaCstaddressinfoController extends BaseController {

    private final IMetaSolanaCstaddressinfoService metaSolanaCstaddressinfoService;

    /**
     * 生成钱包
     */
    @SaIgnore
    @PostMapping("/generate")
    public R<MetaSolanaCstaddressinfoVo> generate(@RequestBody MetaSolanaCstaddressinfoBo bo) {
        MetaSolanaCstaddressinfoVo account = metaSolanaCstaddressinfoService.createAccount(bo);
        return R.ok(account);
    }

    /**
     * 查询Solana客户热钱包信息列表
     */
    @SaCheckPermission("sol:solanaCstaddressinfo:list")
    @GetMapping("/list")
    public TableDataInfo<MetaSolanaCstaddressinfoVo> list(MetaSolanaCstaddressinfoBo bo, PageQuery pageQuery) {
        return metaSolanaCstaddressinfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出Solana客户热钱包信息列表
     */
    @SaCheckPermission("sol:solanaCstaddressinfo:export")
    @Log(title = "Solana客户热钱包信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MetaSolanaCstaddressinfoBo bo, HttpServletResponse response) {
        List<MetaSolanaCstaddressinfoVo> list = metaSolanaCstaddressinfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "Solana客户热钱包信息", MetaSolanaCstaddressinfoVo.class, response);
    }

    /**
     * 获取Solana客户热钱包信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("sol:solanaCstaddressinfo:query")
    @GetMapping("/{id}")
    public R<MetaSolanaCstaddressinfoVo> getInfo(@NotNull(message = "主键不能为空")
                                                 @PathVariable Long id) {
        return R.ok(metaSolanaCstaddressinfoService.queryById(id));
    }

    /**
     * 新增Solana客户热钱包信息
     */
    @SaCheckPermission("sol:solanaCstaddressinfo:add")
    @Log(title = "Solana客户热钱包信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MetaSolanaCstaddressinfoBo bo) {
        return toAjax(metaSolanaCstaddressinfoService.insertByBo(bo));
    }

    /**
     * 修改Solana客户热钱包信息
     */
    @SaCheckPermission("sol:solanaCstaddressinfo:edit")
    @Log(title = "Solana客户热钱包信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MetaSolanaCstaddressinfoBo bo) {
        return toAjax(metaSolanaCstaddressinfoService.updateByBo(bo));
    }

    /**
     * 删除Solana客户热钱包信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("sol:solanaCstaddressinfo:remove")
    @Log(title = "Solana客户热钱包信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(metaSolanaCstaddressinfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
