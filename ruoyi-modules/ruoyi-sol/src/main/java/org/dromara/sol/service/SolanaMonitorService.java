package org.dromara.sol.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.sol.config.SolRpcConfig;
import org.dromara.sol.config.SolanaMonitorConfig;
import org.dromara.sol.constants.SolRedisKeyConstants;
import org.dromara.sol.domain.vo.MetaSolanaCstaddressinfoVo;
import org.dromara.sol.enums.MonitorState;
import org.dromara.sol.enums.SolCoinType;
import org.dromara.sol.event.TransactionEvent;
import org.dromara.sol.event.WalletMonitorEvent;
import org.dromara.sol.manager.SolMonitorManager;
import org.dromara.sol.manager.SolTransactionManager;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

import static org.dromara.sol.enums.SolCoinType.*;

/**
 * Solana监控服务
 * 统一管理WebSocket连接、地址订阅和交易处理
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SolanaMonitorService implements ApplicationRunner {

    private final SolRpcConfig rpcConfig;
    private final SolanaMonitorConfig monitorConfig;
    private final SolMonitorManager solMonitorManager;
    private final SolTransactionManager solTransactionManager;
    private final IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;
    private final RedissonClient redissonClient;
    private final ApplicationEventPublisher eventPublisher;

    // WebSocket客户端
    private WebSocketClient webSocketClient;

    // 当前监控状态
    private final AtomicReference<MonitorState> currentState = new AtomicReference<>(MonitorState.DISCONNECTED);

    // 限流器
    private RRateLimiter rateLimiter;

    // JSON处理器
    private final ObjectMapper objectMapper = new ObjectMapper();

    // 订阅ID映射
    private final Map<String, String> subscriptionAddressMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 初始化限流器
        rateLimiter = redissonClient.getRateLimiter("solana:monitor:rate_limiter");
        rateLimiter.trySetRate(RateType.OVERALL, monitorConfig.getSubscribeRateLimit(),
                              Duration.of(1, ChronoUnit.SECONDS));

        log.info("Solana监控服务初始化完成");
    }

    @Override
    public void run(ApplicationArguments args) {
        setTenant();

        try {
            // 重建监控数据
            int rebuiltCount = solMonitorManager.resetAllMonitoringStatus();
            log.info("系统启动，已从数据库重建{}个地址的监控数据", rebuiltCount);

            // 初始化WebSocket连接
            initializeConnection();

            // 等待连接建立
            if (waitForConnection()) {
                // 订阅所有监控地址
                subscribeAllAddresses();

                // 如果启用补偿，执行一次扫描
                if (monitorConfig.isEnableCompensation()) {
                    performCompensationScan();
                }
            }

        } catch (Exception e) {
            log.error("监控服务启动失败", e);
        }
    }

    /**
     * 初始化WebSocket连接
     */
    @Retryable(value = Exception.class, maxAttempts = 5, backoff = @Backoff(delay = 2000))
    private void initializeConnection() {
        try {
            String websocketUrl = rpcConfig.getWebsocketUrl();
            if (websocketUrl == null || websocketUrl.trim().isEmpty()) {
                throw new IllegalStateException("WebSocket URL未配置");
            }

            setState(MonitorState.CONNECTING);

            webSocketClient = new WebSocketClient(new URI(websocketUrl)) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    handleConnectionOpen(handshake);
                }

                @Override
                public void onMessage(String message) {
                    handleMessage(message);
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    handleConnectionClose(code, reason, remote);
                }

                @Override
                public void onError(Exception ex) {
                    log.error("WebSocket发生错误", ex);
                    setState(MonitorState.ERROR);
                }
            };

            webSocketClient.setConnectionLostTimeout(monitorConfig.getConnectionTimeout());
            webSocketClient.connect();

            log.info("正在连接WebSocket: {}", websocketUrl);

        } catch (Exception e) {
            log.error("初始化WebSocket连接失败", e);
            setState(MonitorState.ERROR);
            throw new RuntimeException("初始化连接失败", e);
        }
    }

    /**
     * 处理连接打开事件
     */
    private void handleConnectionOpen(ServerHandshake handshake) {
        log.info("WebSocket连接已建立，HTTP状态: {}", handshake.getHttpStatus());
        setState(MonitorState.CONNECTED);
    }

    /**
     * 处理连接关闭事件
     */
    private void handleConnectionClose(int code, String reason, boolean remote) {
        log.info("WebSocket连接关闭 [代码: {}, 原因: {}, 远程关闭: {}]", code, reason, remote);
        setState(MonitorState.DISCONNECTED);

        // 如果启用自动重连
        if (monitorConfig.isEnableAutoReconnect()) {
            scheduleReconnect();
        }
    }

    /**
     * 处理WebSocket消息
     */
    private void handleMessage(String message) {
        if (message == null || message.isEmpty()) {
            return;
        }

        try {
            JsonNode rootNode = objectMapper.readTree(message);

            if (rootNode.has("result")) {
                // 处理订阅确认
                handleSubscriptionConfirmation(rootNode);
            } else {
                // 处理交易消息
                handleTransactionMessage(rootNode);
            }

        } catch (JsonProcessingException e) {
            log.error("解析JSON消息失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("处理消息时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 处理订阅确认消息
     */
    private void handleSubscriptionConfirmation(JsonNode rootNode) {
        try {
            String subscriptionId = rootNode.get("result").asText();
            String requestId = rootNode.get("id").asText();

            // 解析币种和钱包ID
            String coinType = null;
            String walletIdStr = null;

            for (SolCoinType solCoinType : values()) {
                String coinCode = solCoinType.getCode();
                if (requestId.startsWith(coinCode)) {
                    coinType = coinCode;
                    walletIdStr = requestId.substring(coinCode.length());
                    break;
                }
            }

            if (coinType != null && walletIdStr != null) {
                long walletId = Long.parseLong(walletIdStr);
                MetaSolanaCstaddressinfoVo wallet = solanaCstaddressinfoService.queryById(walletId);

                if (wallet != null) {
                    String address = getAddressByCoinType(wallet, coinType);
                    if (address != null) {
                        // 更新状态为监控中
                        solMonitorManager.updateAddressStatus(address, SolRedisKeyConstants.STATUS_MONITORING);
                        solMonitorManager.saveSubscriptionMapping(subscriptionId, address);
                        subscriptionAddressMap.put(subscriptionId, address);

                        log.info("地址{}订阅成功，币种: {}", address, coinType);
                    }
                }
            }

        } catch (Exception e) {
            log.error("处理订阅确认消息失败", e);
        }
    }

    /**
     * 处理交易消息
     */
    private void handleTransactionMessage(JsonNode rootNode) {
        try {
            String signature = rootNode.at("/params/result/value/signature").asText(null);
            if (signature != null && !signature.isEmpty()) {
                String subscription = rootNode.at("/params/subscription").asText(null);
                String address = subscriptionAddressMap.get(subscription);

                log.info("收到交易：签名={}, 地址={}", signature, address);

                // 发布交易事件
                eventPublisher.publishEvent(new TransactionEvent(this, signature, address));
            }
        } catch (Exception e) {
            log.error("处理交易消息失败", e);
        }
    }

    /**
     * 订阅所有监控地址
     */
    private void subscribeAllAddresses() {
        try {
            setTenant();
            List<MetaSolanaCstaddressinfoVo> wallets = solanaCstaddressinfoService.queryAll();

            for (MetaSolanaCstaddressinfoVo wallet : wallets) {
                addMonitorAddress(wallet);
            }

            setState(MonitorState.MONITORING);
            log.info("完成所有地址订阅，共处理{}个钱包", wallets.size());

        } catch (Exception e) {
            log.error("订阅所有地址失败", e);
        }
    }

    /**
     * 添加监控地址
     */
    public void addMonitorAddress(MetaSolanaCstaddressinfoVo wallet) {
        try {
            // 添加到Redis监控列表
            solMonitorManager.addMonitorAddress(wallet);

            if (!getCurrentState().canSendMessage()) {
                log.warn("WebSocket未连接，仅将地址添加到监控列表");
                return;
            }

            // 订阅各种币种地址
            Long walletId = wallet.getId();
            subscribeAddressIfExists(wallet.getCstAddress(), walletId, SOL);
            subscribeAddressIfExists(wallet.getCstUsdtAddress(), walletId, USDT);
            subscribeAddressIfExists(wallet.getCstUsdcAddress(), walletId, USDC);

        } catch (Exception e) {
            log.error("添加监控地址失败", e);
        }
    }

    /**
     * 如果地址存在则订阅
     */
    private void subscribeAddressIfExists(String address, Long walletId, SolCoinType coinType) {
        if (address != null && !address.isEmpty()) {
            if (!subscribeAddress(address, walletId, coinType)) {
                solMonitorManager.updateAddressStatus(address, SolRedisKeyConstants.STATUS_FAILED);
            }
        }
    }

    /**
     * 订阅指定地址
     */
    public boolean subscribeAddress(String address, Long walletId, SolCoinType coinType) {
        if (!getCurrentState().canSendMessage()) {
            log.warn("WebSocket状态不允许发送消息: {}", getCurrentState());
            return false;
        }

        try {
            // 限流
            rateLimiter.acquire(1);

            // 构建订阅请求
            String request = buildSubscriptionRequest(address, walletId, coinType.getCode());
            webSocketClient.send(request);

            // 更新状态为待确认
            solMonitorManager.updateAddressStatus(address, SolRedisKeyConstants.STATUS_PENDING);

            log.debug("已发送地址{}的订阅请求，币种: {}", address, coinType.getCode());
            return true;

        } catch (Exception e) {
            log.error("订阅地址{}失败", address, e);
            return false;
        }
    }

    /**
     * 构建订阅请求
     */
    private String buildSubscriptionRequest(String address, Long walletId, String coinType) {
        Map<String, Object> params = Map.of(
            "jsonrpc", "2.0",
            "id", coinType + walletId,
            "method", "logsSubscribe",
            "params", Arrays.asList(
                Map.of("mentions", Arrays.asList(address)),
                Map.of("commitment", "finalized")
            )
        );
        return JsonUtils.toJsonString(params);
    }

    /**
     * 根据币种获取地址
     */
    private String getAddressByCoinType(MetaSolanaCstaddressinfoVo wallet, String coinType) {
        return switch (coinType) {
            case "SOL" -> wallet.getCstAddress();
            case "USDT" -> wallet.getCstUsdtAddress();
            case "USDC" -> wallet.getCstUsdcAddress();
            default -> null;
        };
    }

    /**
     * 等待连接建立
     */
    private boolean waitForConnection() {
        try {
            int timeout = monitorConfig.getConnectionTimeout();
            long startTime = System.currentTimeMillis();

            while (!getCurrentState().isConnected() &&
                   (System.currentTimeMillis() - startTime) < timeout * 1000L) {
                TimeUnit.MILLISECONDS.sleep(100);
            }

            return getCurrentState().isConnected();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 安排重连
     */
    private void scheduleReconnect() {
        new Thread(() -> {
            try {
                TimeUnit.SECONDS.sleep(monitorConfig.getReconnectInterval());
                log.info("尝试重新连接WebSocket...");
                initializeConnection();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }, "solana-reconnect").start();
    }

    /**
     * 执行补偿扫描
     */
    private void performCompensationScan() {
        if (!monitorConfig.isEnableCompensation()) {
            return;
        }

        // 简化版补偿扫描，如果需要可以后续扩展
        log.info("执行遗漏交易补偿扫描");
        // TODO: 实现简化的补偿逻辑
    }

    /**
     * 处理钱包监控事件
     */
    @EventListener
    public void handleWalletMonitorEvent(WalletMonitorEvent event) {
        MetaSolanaCstaddressinfoVo wallet = event.getWallet();
        addMonitorAddress(wallet);
    }

    /**
     * 获取当前状态
     */
    public MonitorState getCurrentState() {
        return currentState.get();
    }

    /**
     * 设置状态
     */
    private void setState(MonitorState state) {
        MonitorState oldState = currentState.getAndSet(state);
        if (oldState != state) {
            log.info("监控状态变更: {} -> {}", oldState.getDescription(), state.getDescription());
            if (state.needsReconnect()){

            }
            if (state.canSendMessage()){
                // 订阅所有监控地址
                setTenant();
                subscribeAllAddresses();
            }
        }
    }

    /**
     * 获取所有监控地址
     */
    public Set<String> getAllMonitorAddresses() {
        return solMonitorManager.getAllMonitorAddresses();
    }

    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return getCurrentState().isConnected();
    }

    private static void setTenant() {
        TenantHelper.setDynamic("000000", true);
    }

    @PreDestroy
    public void cleanup() {
        try {
            if (webSocketClient != null && webSocketClient.isOpen()) {
                webSocketClient.close();
            }
            log.info("SolanaMonitorService清理完成");
        } catch (Exception e) {
            log.error("清理过程中发生错误", e);
        }
    }
}
