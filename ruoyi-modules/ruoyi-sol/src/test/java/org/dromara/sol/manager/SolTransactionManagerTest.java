package org.dromara.sol.manager;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.dromara.sol.service.IMetaSolanaTransactionsService;
import org.dromara.sol.service.IMetaSolanaCstaddressinfoService;
import org.dromara.sol.config.SolWalletConfig;
import org.dromara.sol.mq.NormalRabbitProducer;
import org.p2p.solanaj.rpc.types.SignatureInformation;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * SolTransactionManager 单元测试类
 */
class SolTransactionManagerTest {

    @InjectMocks
    private SolTransactionManager solTransactionManager;

    @Mock
    private SolWalletConfig walletConfig;

    @Mock
    private SolHttpManager solHttpManager;

    @Mock
    private MetaHttpManager metaHttpManager;

    @Mock
    private IMetaSolanaTransactionsService solanaTransactionsService;

    @Mock
    private IMetaSolanaCstaddressinfoService solanaCstaddressinfoService;

    @Mock
    private NormalRabbitProducer normalRabbitProducer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试钱包有交易历史时应返回true")
    void testHasTransactionHistory_WhenWalletHasHistory_ShouldReturnTrue() {
        // 准备测试数据
        String walletAddress = "solana123456";
        List<SignatureInformation> signatures = new ArrayList<>();
        signatures.add(new SignatureInformation()); // 添加一个签名信息

        // 模拟solHttpManager.getSignaturesForAddress返回非空列表
        when(solHttpManager.getSignaturesForAddress(anyString(), anyInt())).thenReturn(signatures);

        // 执行测试
        boolean result = solanaCstaddressinfoService.hasTransactionHistory(walletAddress);

        // 验证结果
        assertTrue(result, "当钱包有交易历史时应返回true");
    }

    @Test
    @DisplayName("测试钱包没有交易历史时应返回false")
    void testHasTransactionHistory_WhenWalletHasNoHistory_ShouldReturnFalse() {
        // 准备测试数据
        String walletAddress = "solana123456";
        List<SignatureInformation> signatures = new ArrayList<>(); // 空列表

        // 模拟solHttpManager.getSignaturesForAddress返回空列表
        when(solHttpManager.getSignaturesForAddress(anyString(), anyInt())).thenReturn(signatures);

        // 执行测试
        boolean result = solanaCstaddressinfoService.hasTransactionHistory(walletAddress);

        // 验证结果
        assertFalse(result, "当钱包没有交易历史时应返回false");
    }

    @Test
    @DisplayName("测试查询交易历史出现异常时应返回true")
    void testHasTransactionHistory_WhenExceptionOccurs_ShouldReturnTrue() {
        // 准备测试数据
        String walletAddress = "solana123456";

        // 模拟solHttpManager.getSignaturesForAddress抛出异常
        when(solHttpManager.getSignaturesForAddress(anyString(), anyInt())).thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        boolean result = solanaCstaddressinfoService.hasTransactionHistory(walletAddress);

        // 验证结果
        assertTrue(result, "当查询交易历史出现异常时应返回true（保守处理）");
    }

    @Test
    @DisplayName("测试金额小于最小入账金额时应返回true")
    void testIsAmountBelowMinimumRecharge_whenAmountIsLess_shouldReturnTrue() {
        // 准备测试数据
        BigDecimal amount = new BigDecimal("1.0");
        BigDecimal minimumRecharge = new BigDecimal("2.0");

        // 执行测试
        boolean result = solTransactionManager.isAmountBelowMinimumRecharge(amount, minimumRecharge);

        // 验证结果
        assertTrue(result, "当金额小于最小入账金额时应返回true");
    }

    @Test
    @DisplayName("测试金额等于最小入账金额时应返回false")
    void testIsAmountBelowMinimumRecharge_whenAmountIsEqual_shouldReturnFalse() {
        // 准备测试数据
        BigDecimal amount = new BigDecimal("2.0");
        BigDecimal minimumRecharge = new BigDecimal("2.0");

        // 执行测试
        boolean result = solTransactionManager.isAmountBelowMinimumRecharge(amount, minimumRecharge);

        // 验证结果
        assertFalse(result, "当金额等于最小入账金额时应返回false");
    }

    @Test
    @DisplayName("测试金额大于最小入账金额时应返回false")
    void testIsAmountBelowMinimumRecharge_whenAmountIsGreater_shouldReturnFalse() {
        // 准备测试数据
        BigDecimal amount = new BigDecimal("3.0");
        BigDecimal minimumRecharge = new BigDecimal("2.0");

        // 执行测试
        boolean result = solTransactionManager.isAmountBelowMinimumRecharge(amount, minimumRecharge);

        // 验证结果
        assertFalse(result, "当金额大于最小入账金额时应返回false");
    }

    @Test
    @DisplayName("测试交易已处理时应返回true")
    void testIsTransactionAlreadyProcessed_whenIssyncIsOne_shouldReturnTrue() {
        // 准备测试数据
        int issync = 1;

        // 执行测试
        boolean result = solTransactionManager.isTransactionAlreadyProcessed(issync);

        // 验证结果
        assertTrue(result, "当issync为1时应返回true");
    }

    @Test
    @DisplayName("测试交易未处理时应返回false")
    void testIsTransactionAlreadyProcessed_whenIssyncIsNotOne_shouldReturnFalse() {
        // 准备测试数据
        int issync = 0;

        // 执行测试
        boolean result = solTransactionManager.isTransactionAlreadyProcessed(issync);

        // 验证结果
        assertFalse(result, "当issync不为1时应返回false");
    }

    @Test
    @DisplayName("测试交易待处理时应返回false")
    void testIsTransactionAlreadyProcessed_whenIssyncIsTwo_shouldReturnFalse() {
        // 准备测试数据
        int issync = 2;

        // 执行测试
        boolean result = solTransactionManager.isTransactionAlreadyProcessed(issync);

        // 验证结果
        assertFalse(result, "当issync为2时应返回false");
    }
}
