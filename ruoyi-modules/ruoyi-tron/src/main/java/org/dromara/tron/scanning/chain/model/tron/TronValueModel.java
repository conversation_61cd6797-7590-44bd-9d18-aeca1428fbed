package org.dromara.tron.scanning.chain.model.tron;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TronValueModel {

    @JsonProperty("amount")
    private BigDecimal amount;

    @JsonProperty("owner_address")
    private String ownerAddress;

    @JsonProperty("to_address")
    private String toAddress;

    @JsonProperty("data")
    private String data;
    @JsonProperty("contract_address")
    private String contractAddress;

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getContractAddress() {
        return contractAddress;
    }

    public void setContractAddress(String contractAddress) {
        this.contractAddress = contractAddress;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getOwnerAddress() {
        return ownerAddress;
    }

    public void setOwnerAddress(String ownerAddress) {
        this.ownerAddress = ownerAddress;
    }

    public String getToAddress() {
        return toAddress;
    }

    public void setToAddress(String toAddress) {
        this.toAddress = toAddress;
    }
}
