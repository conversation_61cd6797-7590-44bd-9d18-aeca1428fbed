package org.dromara.tron.scanning.chain.impl;


import org.dromara.tron.scanning.biz.scan.ScanService;
import org.dromara.tron.scanning.biz.thread.EventQueue;
import org.dromara.tron.scanning.biz.thread.RetryStrategyQueue;
import org.dromara.tron.scanning.biz.thread.model.EventModel;
import org.dromara.tron.scanning.chain.ChainScanner;
import org.dromara.tron.scanning.chain.model.TransactionModel;
import org.dromara.tron.scanning.chain.model.tron.TronBlockModel;
import org.dromara.tron.scanning.chain.model.tron.TronTransactionModel;
import org.dromara.tron.scanning.commons.config.BlockChainConfig;
import org.dromara.tron.scanning.commons.constant.TronConstants;
import org.dromara.tron.scanning.commons.enums.BlockEnums;
import org.dromara.tron.scanning.commons.util.JSONUtil;
import org.dromara.tron.scanning.commons.util.StringUtil;
import org.dromara.tron.scanning.commons.util.okhttp.OkHttpUtil;
import org.dromara.tron.scanning.monitor.TronMonitorEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 扫描Tron链
 */
public class TronChainScanner extends ChainScanner {

    private final Logger logger = LoggerFactory.getLogger(TronChainScanner.class);


    /**
     * TRON节点 url
     */
    private List<String> tronRpcUrls;

    /**
     * 获取Tron监听事件列表
     */
    private List<TronMonitorEvent> tronMonitorEvents;

    @Override
    public void init(BlockChainConfig blockChainConfig, EventQueue eventQueue, RetryStrategyQueue retryStrategyQueue, ScanService scanService) {
        super.init(blockChainConfig, eventQueue, retryStrategyQueue, scanService);

        this.tronRpcUrls = blockChainConfig.getTronRpcUrls();
        this.tronMonitorEvents = blockChainConfig.getEventConfig().getTronMonitorEvents();

    }

    @Override
    public void scan(BigInteger beginBlockNumber) {
        try {
            String url = this.tronRpcUrls.get(getNextIndex(tronRpcUrls.size()));

            BigInteger lastBlockNumber = getBlock(url).getTronBlockHeaderModel().getTronRawDataModel().getNumber();

            if (beginBlockNumber.compareTo(BlockEnums.LAST_BLOCK_NUMBER.getValue()) == 0) {
                beginBlockNumber = lastBlockNumber;
            }
            if (beginBlockNumber.intValue() == 0) {
                beginBlockNumber = lastBlockNumber;
            }

            if (scanService.getCurrentBlockHeight() == null) {
                scanService.setCurrentBlockHeight(lastBlockNumber);
            }

            if (beginBlockNumber.compareTo(lastBlockNumber) > 0) {
                logger.debug("[TRON], 链上的块高度已经落后于块扫描进度，暂停正在进行的扫描...... , 扫描进度[{}], 链上的最新块高度：[{}]", beginBlockNumber, lastBlockNumber);
                return;
            }

            if (blockChainConfig.getEndBlockNumber().compareTo(BigInteger.ZERO) > 0
                && beginBlockNumber.compareTo(blockChainConfig.getEndBlockNumber()) >= 0) {
                logger.debug("[TRON], 当前区块高度已达到您设置的停止区块高度，因此扫描作业已自动停止，扫描进度[{}]，链上的结束区块高度：[{}] ", beginBlockNumber, blockChainConfig.getEndBlockNumber());
                //todo-tron
//                String redisKey = RedisConstants.BIZ_BLOCK_NUMBER_TRON_COMPENSATE + blockChainConfig.getEndBlockNumber();
//                StringRedisTemplate redisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
//                if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
//                    redisTemplate.delete(redisKey);
//                }

                scanService.shutdown();
                return;
            }

            TronBlockModel tronBlockModel = getBlockByNum(url, beginBlockNumber);
            if (tronBlockModel == null) {
                logger.debug("[TRON], 块高度[{}]不存在", beginBlockNumber);
                if (lastBlockNumber.compareTo(beginBlockNumber) > 0) {
                    blockChainConfig.setBeginBlockNumber(beginBlockNumber.add(BigInteger.ONE));
                    //如果跳过该块，则需要通知重试策略
                    addRetry(beginBlockNumber);
                }
                scanService.setCurrentBlockHeight(lastBlockNumber);
                return;
            }

            List<TronTransactionModel> tronTransactionList = tronBlockModel.getTransactions();
            if (tronTransactionList == null || tronTransactionList.isEmpty()) {
                logger.debug("[TRON]，在区块高度 [{}] 上没有扫描到交易。", beginBlockNumber);
                if (lastBlockNumber.compareTo(beginBlockNumber) > 0) {
                    blockChainConfig.setBeginBlockNumber(beginBlockNumber.add(BigInteger.ONE));

                    //如果块跳过，则需要通知重试策略
                    addRetry(beginBlockNumber);
                }
                scanService.setCurrentBlockHeight(lastBlockNumber);
                return;
            }


            List<TransactionModel> transactionList = new ArrayList<>();

            for (TronTransactionModel transaction : tronTransactionList) {
                if (transaction == null) {
                    continue;
                }
                transaction.setBlockID(tronBlockModel.getBlockID());
                transaction.setTronBlockHeaderModel(tronBlockModel.getTronBlockHeaderModel());
                transactionList.add(
                    TransactionModel.builder()
                        .setTronTransactionModel(transaction)
                );
            }

            eventQueue.add(EventModel.builder()
                .setCurrentBlockHeight(beginBlockNumber)
                .setTransactionModels(transactionList)
            );


            blockChainConfig.setBeginBlockNumber(beginBlockNumber.add(BigInteger.ONE));

            scanService.setCurrentBlockHeight(lastBlockNumber);

        } catch (Exception e) {
            logger.error("[TRON]，在扫描时发生了异常，当前区块高度为：[{}]", beginBlockNumber, e);
        }
    }

    @Override
    public void call(TransactionModel transactionModel) {

        for (TronMonitorEvent tronMonitorEvent : this.tronMonitorEvents) {
            try {
                tronMonitorEvent.call(transactionModel);
            } catch (Exception e) {
                logger.error("[Tron]，监听器调用方法异常", e);
            }
        }
    }

    /**
     * 获取最新的块
     */
    private TronBlockModel getBlock(String url) throws Exception {
        String result = OkHttpUtil.postJson(url + TronConstants.GET_NOW_BLOCK, TronConstants.GET_NOW_BLOCK_PARAMETER);
        if (StringUtil.isEmpty(result)) {
            throw new Exception("获取最新块时发生异常，result: null");
        }
        return JSONUtil.toJavaObject(result, TronBlockModel.class);
    }

    /**
     * 根据区块以高速获取区块信息。
     */
    private TronBlockModel getBlockByNum(String url, BigInteger blockNumber) throws Exception {
        Map<String, Object> parameter = new HashMap<>();
        parameter.put("num", blockNumber);

        String result = OkHttpUtil.postJson(url + TronConstants.GET_BLOCK_BY_NUM, parameter);
        if (StringUtil.isEmpty(result)) {
            throw new Exception("根据块高度查询块时异常，结果为null。处理步骤");
        }
        return JSONUtil.toJavaObject(result, TronBlockModel.class);
    }
}
