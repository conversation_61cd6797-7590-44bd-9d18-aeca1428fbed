package org.dromara.tron.scanning.biz.thread;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 用于执行事件消费者的线程池
 */
public class EventThreadPool {

    /**
     * 用于执行事件消费者的线程池
     */
    private static ThreadPoolExecutor threadPoolExecutor;

    /**
     * 用于记录当前正在执行的扫描任务数量
     */
    private static AtomicInteger numberOfActive;

    /**
     * 初始化线程池
     * @param corePoolSize 必须大于等于任务数量，建议等于任务数量
     */
    public static void init(int corePoolSize) {
        if (threadPoolExecutor == null) {
            threadPoolExecutor = new ThreadPoolExecutor(corePoolSize, corePoolSize * 2, 3, TimeUnit.SECONDS, new LinkedBlockingQueue<>(corePoolSize * 10));
        }
        if(numberOfActive == null){
            numberOfActive = new AtomicInteger(0);
        }
    }

    /**
     * 添加事件消费者任务
     * @param runnable
     * @throws Exception
     */
    public static void submit(Runnable runnable) throws Exception {
        if(threadPoolExecutor == null){
            throw new Exception("执行扫描任务需要先初始化线程池, 可调用EventThreadPool.init(1)方法进行初始化");
        }
        threadPoolExecutor.submit(runnable);
    }

    /**
     * 记录任务数量
     */
    public static void incrementTaskNumber(){
        numberOfActive.incrementAndGet();
    }

    /**
     * 如果剩余任务少于2个，则关闭整个线程池
     */
    public static synchronized void shutdownTask(){
        if(numberOfActive == null){
            shutdown();
            return;
        }

        int number = numberOfActive.get();

        if(number > 0){
            numberOfActive.decrementAndGet();
        }

        if(number <= 1){
            shutdown();
        }
    }

    /**
     * 停止线程池
     */
    public static void shutdown(){
        if (threadPoolExecutor != null) {
            threadPoolExecutor.shutdown();
            threadPoolExecutor = null;
        }
    }
}
