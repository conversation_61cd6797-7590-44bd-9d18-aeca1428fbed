package org.dromara.tron.scanning.chain;


import org.dromara.tron.scanning.biz.scan.ScanService;
import org.dromara.tron.scanning.biz.thread.EventQueue;
import org.dromara.tron.scanning.biz.thread.RetryStrategyQueue;
import org.dromara.tron.scanning.chain.model.TransactionModel;
import org.dromara.tron.scanning.commons.config.BlockChainConfig;
import org.dromara.tron.scanning.commons.enums.BlockEnums;
import org.dromara.tron.scanning.commons.enums.ChainType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigInteger;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 主要用于扫描的类，目前有 3 个实现类，未来可以扩展
 */
public abstract class ChainScanner {

    private Logger logger = LoggerFactory.getLogger(ScanService.class);

    /**
     * 配置此区块扫描任务所需的参数
     */
    protected BlockChainConfig blockChainConfig;

    /**
     * 队列，每次扫描到一个区块，就会将任务放入其中，扫描的数据将被异步处理，并根据需要调用 MonitorEvent
     */
    protected EventQueue eventQueue;

    /**
     * 队列，当某个区块高度因某些原因被跳过且用户已设置重试策略时，跳过的区块高度将被放入此队列并等待重试
     */
    protected RetryStrategyQueue retryStrategyQueue;

    /**
     * 执行区块扫描的业务类
     */
    protected ScanService scanService;

    /**
     * 用于实现轮询负载均衡
     */
    private AtomicInteger atomicInteger;

    /**
     * 获取链类型
     *
     * @return
     */
    public ChainType getChainType() {
        return blockChainConfig.getChainType();
    }

    /**
     * 开始扫描
     */
    public void scanStart() {
        String logBeginBlockNumber = blockChainConfig.getBeginBlockNumber()
            .compareTo(BlockEnums.LAST_BLOCK_NUMBER.getValue()) == 0 ?
            BlockEnums.LAST_BLOCK_NUMBER.getText() :
            blockChainConfig.getBeginBlockNumber().toString();

        logger.debug("开始扫描, 链类型: {}, 起始区块高度: {}", blockChainConfig.getChainType().toString(), logBeginBlockNumber);

        try {
            scan(blockChainConfig.getBeginBlockNumber());
        } catch (Exception e) {
            logger.error("扫描过程中发生异常, 链类型: {}, 起始区块高度: {}", blockChainConfig.getChainType().toString(), logBeginBlockNumber, e);
        }
    }

    /**
     * 初始化所有成员变量
     *
     * @param blockChainConfig   区块链配置
     * @param eventQueue         事件队列
     * @param retryStrategyQueue 重试策略队列
     * @param scanService        扫描服务
     */
    public void init(BlockChainConfig blockChainConfig, EventQueue eventQueue, RetryStrategyQueue retryStrategyQueue, ScanService scanService) {
        if (this.blockChainConfig == null) {
            this.blockChainConfig = blockChainConfig;
        }

        if (this.eventQueue == null) {
            this.eventQueue = eventQueue;
        }

        if (this.retryStrategyQueue == null) {
            this.retryStrategyQueue = retryStrategyQueue;
        }

        if (this.scanService == null) {
            this.scanService = scanService;
        }

        this.atomicInteger = new AtomicInteger(0);
    }

    /**
     * 添加需要重试的区块高度
     *
     * @param blockNumber 区块高度
     */
    protected void addRetry(BigInteger blockNumber) {
        if (this.retryStrategyQueue != null) {
            this.retryStrategyQueue.add(blockNumber);
        }
    }

    /**
     * 通过轮询获取节点索引
     *
     * @return 索引
     */
    protected int getNextIndex(int maxValue) {
        int index = atomicInteger.get();

        if (index < (maxValue - 1)) {
            atomicInteger.incrementAndGet();
        } else {
            atomicInteger.set(0);
        }
        return index;
    }

    /**
     * 扫描区块
     *
     * @param beginBlockNumber 起始区块高度
     */
    public abstract void scan(BigInteger beginBlockNumber);

    /**
     * 处理扫描到的交易数据，并按需执行监控事件
     *
     * @param transactionModel 交易数据模型
     */
    public abstract void call(TransactionModel transactionModel);
}
