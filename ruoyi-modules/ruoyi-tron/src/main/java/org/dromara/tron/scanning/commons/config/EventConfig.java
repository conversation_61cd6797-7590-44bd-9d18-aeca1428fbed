package org.dromara.tron.scanning.commons.config;



import org.dromara.tron.scanning.monitor.EthMonitorEvent;
import org.dromara.tron.scanning.monitor.TronMonitorEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * 监听事件集
 */
public class EventConfig {

    /**
     * ETH 监听事件
     */
    private List<EthMonitorEvent> ethMonitorEventList = new ArrayList<>();

    /**
     * Tron 监听事件
     */
    private List<TronMonitorEvent> tronMonitorEvents = new ArrayList<>();

    public List<EthMonitorEvent> getEthMonitorEvent() {
        return ethMonitorEventList;
    }

    public void addEthMonitorEvent(EthMonitorEvent ethMonitorEvent) {
        ethMonitorEventList.add(ethMonitorEvent);
    }

    public List<TronMonitorEvent> getTronMonitorEvents() {
        return tronMonitorEvents;
    }

    public void addTronMonitorEvents(TronMonitorEvent tronMonitorEvent) {
        this.tronMonitorEvents.add(tronMonitorEvent);
    }
}
