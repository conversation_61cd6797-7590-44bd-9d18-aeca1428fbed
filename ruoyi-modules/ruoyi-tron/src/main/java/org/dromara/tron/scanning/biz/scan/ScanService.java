package org.dromara.tron.scanning.biz.scan;


import org.dromara.tron.scanning.biz.thread.*;
import org.dromara.tron.scanning.chain.ChainScanner;
import org.dromara.tron.scanning.chain.RetryStrategy;
import org.dromara.tron.scanning.chain.factory.ChainScannerFactory;
import org.dromara.tron.scanning.commons.config.BlockChainConfig;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.math.BigInteger;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

/**
 * 用于扫描块的业务类
 */
public class ScanService {

    private Logger logger = LoggerFactory.getLogger(ScanService.class);

    /**
     * 主要用于扫描的类，目前有3个实现类，以后可以扩展
     */
    private ChainScanner chainScanner;

    /**
     * 重试策略，用于恢复跳过块高度
     */
    private RetryStrategy retryStrategy;

    /**
     * 配置此块扫描任务所需的参数
     */
    private BlockChainConfig blockChainConfig;
    /**
     * 队列，每次扫描一个块时，将在其中放置一个任务，扫描的数据将被异步处理，并根据需要调用MonitorEvent
     */
    private EventQueue eventQueue;

    /**
     * 执行扫描任务的定时任务
     */
    @Getter
    private Timer timer;

    /**
     * 监控事件消费者
     */
    @Getter
    private EventConsumer eventConsumer;

    /**
     * 重试策略消费者
     */
    @Getter
    private RetryStrategyConsumer retryStrategyConsumer;

    /**
     * 队列，当由于某种原因跳过块高度并且用户设置了重试策略时，跳过的块高度将被放置在此队列中并等待重试。
     */
    protected RetryStrategyQueue retryStrategyQueue;

    /**
     * 到目前为止已扫描的最大块高度
     */
    private BigInteger currentBlockHeight;

    private final BigInteger mod = new BigInteger("20");

    public void setCurrentBlockHeight(BigInteger currentBlockHeight) {
        this.currentBlockHeight = currentBlockHeight;
    }

    public BigInteger getCurrentBlockHeight() {
        if (currentBlockHeight == null) {
            return blockChainConfig.getBeginBlockNumber();
        }
        return currentBlockHeight;
    }

    /**
     * 初始化所有成员变量
     */
    public void init(BlockChainConfig blockChainConfig) throws Exception {
        this.blockChainConfig = blockChainConfig;
        this.chainScanner = ChainScannerFactory.getChainScanner(this.blockChainConfig.getChainType());
        this.retryStrategy = this.blockChainConfig.getRetryStrategy();
        this.eventQueue = new EventQueue();

        if (this.retryStrategy != null) {
            this.retryStrategyQueue = new RetryStrategyQueue();
        }

        chainScanner.init(blockChainConfig, eventQueue, retryStrategyQueue, this);

        eventConsumer = new EventConsumer(chainScanner, eventQueue);
        EventThreadPool.submit(eventConsumer);

        if (this.retryStrategy != null) {
            retryStrategyConsumer = new RetryStrategyConsumer(retryStrategyQueue, retryStrategy);
            EventThreadPool.submit(retryStrategyConsumer);
        }

        EventThreadPool.incrementTaskNumber();
    }

    /**
     * 启动自动扫描
     */
    public void start() {
        timer = new Timer();
        timer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                chainScanner.scanStart();
            }
        }, new Date(System.currentTimeMillis() + blockChainConfig.getDelayed()), blockChainConfig.getScanPeriod());

        logger.info("{} 扫描任务将在 {} 毫秒后开始。", blockChainConfig.getChainType().name(), blockChainConfig.getDelayed());
    }

    public void shutdown() {
        Timer timer = this.getTimer();
        if (timer != null) {
            timer.cancel();
        }

        EventConsumer eventConsumer = this.getEventConsumer();
        if (eventConsumer != null) {
            eventConsumer.setShutdown(true);
        }

        RetryStrategyConsumer retryStrategyConsumer = this.getRetryStrategyConsumer();
        if (retryStrategyConsumer != null) {
            retryStrategyConsumer.setShutdown(true);
        }

        EventThreadPool.shutdownTask();
    }
}
