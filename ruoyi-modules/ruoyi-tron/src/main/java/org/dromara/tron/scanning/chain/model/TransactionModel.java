package org.dromara.tron.scanning.chain.model;


import org.dromara.tron.scanning.chain.model.eth.EthTransactionModel;
import org.dromara.tron.scanning.chain.model.tron.TronTransactionModel;

/**
 * 扫描到的交易结果
 */
public class TransactionModel {

    /**
     * 以太坊链上的交易对象
     */
    private EthTransactionModel ethTransactionModel;

    /**
     * 波场链上的交易信息
     */
    private TronTransactionModel tronTransactionModel;

    // TODO SOL 正在开发中，所以暂时没有结果集属性

    public static TransactionModel builder(){
        return new TransactionModel();
    }

    public EthTransactionModel getEthTransactionModel() {
        return ethTransactionModel;
    }

    public TransactionModel setEthTransactionModel(EthTransactionModel ethTransactionModel) {
        this.ethTransactionModel = ethTransactionModel;
        return this;
    }

    public TronTransactionModel getTronTransactionModel() {
        return tronTransactionModel;
    }

    public TransactionModel setTronTransactionModel(TronTransactionModel tronTransactionModel) {
        this.tronTransactionModel = tronTransactionModel;
        return this;
    }
}
