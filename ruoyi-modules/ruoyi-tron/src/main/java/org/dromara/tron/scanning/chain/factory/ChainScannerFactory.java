package org.dromara.tron.scanning.chain.factory;


import cn.hutool.extra.spring.SpringUtil;
import org.dromara.tron.scanning.chain.ChainScanner;
import org.dromara.tron.scanning.chain.impl.ETHChainScanner;
import org.dromara.tron.scanning.chain.impl.SolChainScanner;
import org.dromara.tron.scanning.chain.impl.TronChainScanner;
import org.dromara.tron.scanning.commons.enums.ChainType;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Factory class, get scanner
 */
public class ChainScannerFactory {


    /**
     * get scanner
     * @param chainType
     * @return
     */
    public static ChainScanner getChainScanner(ChainType chainType) {
        switch (chainType) {
            case ETH:
                return new ETHChainScanner();
            case SOL:
                return new SolChainScanner();
            case TRON:
                return new TronChainScanner();
//                return SpringUtil.getBean(TronChainScanner.class);
        }

        return null;
    }
}
