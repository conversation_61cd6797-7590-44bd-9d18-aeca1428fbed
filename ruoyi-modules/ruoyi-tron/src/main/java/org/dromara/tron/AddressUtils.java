package org.dromara.tron;

import com.google.protobuf.ByteString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.exception.base.BaseException;
import org.tron.trident.abi.datatypes.Address;
import org.tron.trident.core.ApiWrapper;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class AddressUtils {

    // 获取智能合约的data
    public static String toHex(String address, Integer quantity) {
        ByteString addressByteString = ApiWrapper.parseAddress(address);
        String addressHex = ApiWrapper.toHex(addressByteString);

        String quantityHex = Integer.toHexString(quantity);
        String addressStr = StringUtils.leftPad(addressHex, 64, "0");
        String quantityStr = StringUtils.leftPad(quantityHex, 64, "0");

        return addressStr + quantityStr;
    }

    // 解析智能合约data获取地址和转账数量  136位
    public static Map<String, String> decryptHex(String data, String txid) {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isBlank(data) || StringUtils.isBlank(txid)) {
            return map;
        }

        try {
            String addressStr = "";
            String quantityStr = "";
            BigInteger feeBigInteger = null;
            if (data.length() == 136) {
                addressStr = data.substring(8, 72);
                quantityStr = data.substring(72);
            } else if (data.length() == 128) {
                addressStr = data.substring(0, 64);
                quantityStr = data.substring(64, 128);
            }
            //gasFree下data长度
            else if (data.length() == 840) {
                String newData = data.substring(8);
                addressStr = newData.substring(2 * 64, 3 * 64);
                quantityStr = newData.substring(3 * 64, 4 * 64);
                String feeStr = newData.substring(4 * 64, 5 * 64);
                feeBigInteger = new BigInteger(feeStr, 16);
            } else if (data.length() == 200) {
                addressStr = data.substring(8, 72);
                quantityStr = data.substring(data.length() - 64);
            } else {
                throw new BaseException("异常长度");
            }
            if (quantityStr.startsWith("ffffff") || !quantityStr.startsWith("0000000000000000000000000000000000000000")) {
//                log.debug("交易金额异常（可能是授权交易），txid:{}，金额:{}", txid, quantityStr);
                return null;
            }
            BigInteger coinBigInteger = new BigInteger(quantityStr, 16);
            if (coinBigInteger.compareTo(new BigInteger("100000000000")) > 0) {
//                log.error("交易金额过大:txid:{}，金额:{}", txid, coinBigInteger);
                return null;
            }

            String toAddress = new Address(200, addressStr).getValue();

            map.put("quantity", coinBigInteger.toString());
            map.put("address", toAddress);
            map.put("length", String.valueOf(data.length()));
            map.put("fee", feeBigInteger != null ? feeBigInteger.toString() : "");
        } catch (Exception e) {
            log.error(e.getMessage());
            log.error("length:{},txid:{},data:{}", data.length(), txid, data);
        }

        return map;
    }


    public static void main(String[] args) {

//        AddressUtils.decryptHex("23b872dd000000000000000000000000a3b8f09a6fce3562b8808d75557fa6d630ec1fc70000000000000000000000008f483a9549a3c10f99a1da9da03a05c634be9abf000000000000000000000000000000000000000000000000000000000fcb9440 "
//                , "3d23f920bc568e0475b12bf9c09e8fe1d8377d78034300a078d0b72abaf4be79");

//        System.out.println(decryptHex(
//                "a9059cbb000000000000000000000000990c4236f69588e591f346ce84cc3bbb467b0446000000000000000000000000000000000000000000000000000000000bebc200",
//                "sdf"));

        System.out.println(new Address("413b415050b1e79e38507cb6e48dacc227affdd50c"));

        String data = "6f21b898000000000000000000000000a614f803b6fd780986a42c78ec9c7f77e6ded13c00000000000000000000000054de13134a4ca7ac01c418ef0e55aa88a4a537cb000000000000000000000000084ca4b98069856f68a19c247f3514f4e0fa23ae000000000000000000000000000000000000000000000000000000002d4cae0000000000000000000000000000000000000000000000000000000000000f42400000000000000000000000000000000000000000000000000000000067e1a3300000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000001200000000000000000000000000000000000000000000000000000000000000041e1ae265898aa3aa8c32b50d84a2f5eab0904391d9c1ea707238bd7e806f642383df6096c4a55cd4bcc10e8073e8551f297a7af45ad03286e00bb0712107d5aa01c00000000000000000000000000000000000000000000000000000000000000";
        if (data.length() % 64 == 0) {
            System.out.println("标准 32 字节对齐数据");
        } else {
            data = data.substring(8);
            System.out.println("非标准长度数据，可能包含额外信息");
        }

        ArrayList<String> chunks = new ArrayList<>();
        // 逐个解析 64 字节块
        for (int i = 0; i < data.length(); i += 64) {
            String chunk = data.substring(i, Math.min(i + 64, data.length()));
            System.out.println("块：" + chunk);
            chunks.add(chunk);
        }


    }


}
