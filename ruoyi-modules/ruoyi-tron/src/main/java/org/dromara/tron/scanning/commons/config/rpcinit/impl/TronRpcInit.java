package org.dromara.tron.scanning.commons.config.rpcinit.impl;


import org.dromara.tron.scanning.commons.config.rpcinit.RpcInit;

/**
 * 设置Tron的RPC地址
 */
public class TronRpcInit extends RpcInit {

    public static TronRpcInit create() {
        return new TronRpcInit();
    }

    /**
     * 设置节点url
     *
     */
    public TronRpcInit addRpcUrl(String rpcUrl) throws Exception {
        blockChainConfig.addTronRpcUrls(rpcUrl);
        return this;
    }
}
