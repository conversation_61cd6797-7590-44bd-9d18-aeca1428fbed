package org.dromara.tron.scanning.chain.impl;



import org.dromara.tron.scanning.chain.ChainScanner;
import org.dromara.tron.scanning.chain.model.TransactionModel;

import java.math.BigInteger;

/**
 * scan the sol chain
 *
 * TODO In development.......
 */
public class SolChainScanner extends ChainScanner {

    @Override
    public void scan(BigInteger beginBlockNumber) {

    }

    @Override
    public void call(TransactionModel transactionModel) {

    }
}
