# curl https://capable-sleek-sponge.solana-devnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/
#  -X POST
#  -H "Content-Type: application/json"
#  --data '{"jsonrpc": "2.0","id": 1,"method": "getTokenAccountsByOwner","params": ["FAhKfsjR17ad8pYjvypXY7JHbmyyR6deCdtpZTJBjs6m",{"mint": "Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr"},{"encoding": "jsonParsed"}]}'
POST https://capable-sleek-sponge.solana-devnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/
Content-Type: application/json

{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "getTokenAccountsByOwner",
    "params": [
        "FAhKfsjR17ad8pYjvypXY7JHbmyyR6deCdtpZTJBjs6m",
        {
            "mint": "Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr"
        },
        {
            "encoding": "jsonParsed"
        }
    ]
}

###

