package org.dromara.test.sol;

import org.dromara.sol.manager.SolTransactionManager;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2025/4/8 16:28
 **/
@SpringBootTest
@DisplayName("Sol单元测试")
public class SolUnitTest {

//    private static final Logger logger = LoggerFactory.getLogger(SolUnitTest.class);
//    private static final String URL = "https://capable-sleek-sponge.solana-devnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/";
//    private static final String TOKEN_MINT_ADDRESS = "Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr";
//
//    private static final String USER1_ADDRESS = "4CPiGUw9MxGvt2qPTVnuwYqUBvkeh7atZbeKcNGNMAe9";
//    private static final String USER1_PRIVATE_KEY = "498ivG7zFSLNBX4YBsTDgQPLTbyEPqz2TzaqXCNANSsXVW3m4QVTZBeMLNFvsMrWsPTd2cfRwv8kRyi8XoYq87ws";
//    private static final String USER2_ADDRESS = "FAhKfsjR17ad8pYjvypXY7JHbmyyR6deCdtpZTJBjs6m";

    @Autowired
    private SolTransactionManager solTransactionManager;

    @Test
    @DisplayName("通过交易哈希解析交易记录(swap)")
    void getTokenBalance() {
//        solTransactionManager.callTransactionBySignature("4ZLVtyrbs3UqRfCMVeniWAN5bcRH64t4ugGc8TG7x1e4bkUULYPaUDYpeqCEtNpt7orKsT6YYdca8SzSVkH3ytnJ");
//        solTransactionManager.callTransactionBySignature("3AZHMcGk5RmXJdWA8TJSy4SM52nZhAxV5FEf5FkeAySZevxV6WwyQEx84TEXbAb9dXKW2UPhNRhHxgasjX8GAxmT");
//        solTransactionManager.callTransactionBySignature("3A5zFgiynjXdBSPUSrgT2LqktFgPNCijQo6RF9p5zwvo3wfQt51TMtqH3GR2UrZcKojeFdiWTgQBoYZBnfkgnPRk");
//        solTransactionManager.callTransactionBySignature("4KkD7xwEWqdDXrm2HZiqvMrSpg2ARj6EXEKXHWibKWGqhUcMzjmGCh3iBZSrJFCxqrQfukxCRmwogXKhECo37YW4");
//        SolHttpUtil.transferToken(
//            ,
//            mainAddress,
//            uiAmount.multiply(new BigDecimal("1000000")).toBigInteger().longValue(),
//            metaSolanaTransactionBo.getContract()
//        );
    }


//    MockedStatic<SolHttpUtil> solHttpUtilMock;

//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//        solHttpUtilMock = mockStatic(SolHttpUtil.class);
//        solHttpUtilMock.when(SolHttpUtil::getHttpHeaders).thenCallRealMethod();
//        solHttpUtilMock.when(() -> SolHttpUtil.getResponseEntity(any(), any(), any())).thenCallRealMethod();
//        solHttpUtilMock.when(() -> SolHttpUtil.getSlot(any())).thenCallRealMethod();
//        solHttpUtilMock.when(() -> SolHttpUtil.getSlotContent(any(), anyLong())).thenCallRealMethod();
//        solHttpUtilMock.when(() -> SolHttpUtil.transfer(any(), any(), any(), anyLong())).thenCallRealMethod();
//        solHttpUtilMock.when(() -> SolHttpUtil.transferToken(any(), any(), any(), anyLong(), any())).thenCallRealMethod();
//        solHttpUtilMock.when(() -> SolHttpUtil.getBalance(any(), any())).thenCallRealMethod();
//        solHttpUtilMock.when(() -> SolHttpUtil.getTokenBalance(any(), any(), any())).thenCallRealMethod();
//    }
//
//    @Test
//    @DisplayName("测试SOL获取最新区块方法")
//    void getSlotForSol() {
//        logger.info("Slot for SOL: {}", SolHttpUtil.getSlot(URL));
//    }
//
//    @Test
//    @DisplayName("测试SOL获取指定区块内容方法")
//    void getSlotContentForSol() {
////        logger.info("Slot content for SOL: {}", SolHttpUtil.getSlotContentForSol(URL, 372591370));
//
////        long slot = SolHttpUtil.getSlot(URL);
//
//        //铸造货币：需要注意pre里面没有
//        Block slotContent = SolHttpUtil.getSlotContent(URL, 373059538);
////        //账户1转账户2（api）
////        Block slotContent = SolHttpUtil.getSlotContent(URL, 373673383);
////        //账户2转账户1（dapp插件）
////        Block slotContent = SolHttpUtil.getSlotContent(URL, 374375074);
//        for (ConfirmedTransaction transaction : slotContent.getTransactions()) {
//            if (!transaction.getMeta().getPreTokenBalances().isEmpty()) {
//                System.out.println(transaction);
//            }
//        }
//
////        RpcClient client = new RpcClient(URL);
////        ConfirmedTransaction transaction = client.getApi().getTransaction("3ZcCbxYgZDFqKETDSNdcWpJYzswjkmkhACxTJLSywz2k8uKkxLL7vYJWGp8LxNFE2TVYKWdLaaFtw2fB6S5RndAY");
//
//
//        logger.info("Slot content for SOL: {}", slotContent);
//    }
//
//    @Test
//    @DisplayName("测试SOL交易方法")
//    void getTransfer() {
//        logger.info("SOL交易结果: {}", SolHttpUtil.transfer(
//                URL,
//                USER1_PRIVATE_KEY,
//                USER2_ADDRESS,
//                1L
//            )
//        );
//    }
//
//    @Test
//    @DisplayName("测试SOL交易Token方法")
//    void getTransferToken() {
//        logger.info("Token交易结果: {}", SolHttpUtil.transferToken(
//                URL,
//                USER1_PRIVATE_KEY,//from account 的私钥
//                USER2_ADDRESS, //to account 的 token account
//                1000000L,
//                TOKEN_MINT_ADDRESS
//            )
//        );
//    }
//
//    @Test
//    @DisplayName("测试获取余额方法")
//    void getBalance() {
//        logger.info("SOL余额结果: {}", SolHttpUtil.getBalance(
//                URL,
//                USER1_ADDRESS
//            )
//        );
//    }
//
//    @SneakyThrows
//    @Test
//    @DisplayName("测试获取Token余额方法")
//    void getTokenBalance() {
//        logger.info("Token余额结果: {}", SolHttpUtil.getTokenBalance(
//                URL,
//                USER1_ADDRESS,
//                TOKEN_MINT_ADDRESS
//            )
//        );
//    }
//
//    @AfterEach
//    void tearDown() {
//        if (solHttpUtilMock != null) {
//            solHttpUtilMock.close();
//        }
//    }
//
//    public static void main(String[] args) {
//
//        try {
//            RpcClient client = new RpcClient("https://methodical-twilight-dream.solana-mainnet.quiknode.pro/2f8a0529513b6768ef6920d5511677bd56852aba/");
//            //测试网
////            RpcClient client = new RpcClient("https://capable-sleek-sponge.solana-devnet.quiknode.pro/e0946ba9546e5a1119b2b904ed407c0e7447bc5b/");
//
//
//            ArrayList<ConfirmedTransaction> confirmedTransactionList = new ArrayList<>();
//            confirmedTransactionList.add(client.getApi().getTransaction("T6JWAZYVth6tHLVXUy1KiZ5Zj1jsLVgwjf6pPVuBfi3UWU83RBrLPrz8W7aw4sBDzQgMstm4T8v7n28M7L95KzQ"));
////            //模拟sol交易
////            confirmedTransactionList.add(client.getApi().getTransaction("3QTMVbZiDpDD2VqGvqBnTPddHMfaLvcDdD4PP8V8VpKL9bVEJxudUy72z9CRRhuYcvF7KXBHNqyB5qaHQrueeBhx"));
////            //模拟token transfer交易
////            confirmedTransactionList.add(client.getApi().getTransaction("2ms36BoYX9471y1aypV7ESbrRuK8nBTuJzzuJ7JMpBhF4DCmSyX8NaZZDRqBK4qjmvrnK2NygAn6Qs8hWB32oifm"));
////            //模拟token transferChecked交易
////            confirmedTransactionList.add(client.getApi().getTransaction("2zzWrAtEydqTf4By4HsfWjJvXtQu5SDsEJ5SivvN7PuEpHoYMN5bU7s898ETVZbJK9huWz8t41KxMFUborTn3uqv"));
//
//
//            for (ConfirmedTransaction transaction : confirmedTransactionList) {
//
//                System.out.println(transaction.getMeta().getPreBalances());
//                System.out.println(transaction.getMeta().getPostBalances());
//                System.out.println(transaction.getMeta().getPreTokenBalances());
//                System.out.println(transaction.getMeta().getPostTokenBalances());
//
//                List<ConfirmedTransaction.Instruction> instructions = transaction.getTransaction().getMessage().getInstructions();
//                instructions.forEach(item -> {
//
//                    String programId = transaction.getTransaction().getMessage().getAccountKeys().get((int) item.getProgramIdIndex());
//                    byte[] decodedData = Base58.decode(item.getData());
//
//                    transaction.getMeta().getPreTokenBalances().forEach(item2 -> {
//                        if (item2.getMint().equals("Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr")) {
//                            //提取涉及的token地址
//                            String address = transaction.getTransaction().getMessage().getAccountKeys().get(item2.getAccountIndex().intValue());
//                            System.out.println(address);
//                            //判断是否是用户token地址(注意代币类型）
//
//
//                        }
//                    });
//                    switch (programId) {
//                        case "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL" -> {
//                            logger.info("创建账户");
//
//                            logger.info("授权账户source：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(0))));
//                            logger.info("新账户Account：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(1))));
//                            logger.info("新账号归属账户wallet：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(2))));
//                            logger.info("代币mint：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(3))));
//                        }
//                        case "********************************" -> {
//                            logger.info("系统程序");
//
//                            int instructionType = decodedData[0] & 0xFF;
//                            logger.info("系统指令类型: {}", instructionType);
//                            if (instructionType == 2) {
//                                logger.info("系统指令类型检测到Transfer指令:{}", instructionType);
//
//                                logger.info("授权账户Source：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(0))));
//                                logger.info("目标账户Destination：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(1))));
//
//                                // For SOL transfers, the amount is in the instruction data bytes 1-8 (little endian)
//                                byte[] amountBytes = Arrays.copyOfRange(decodedData, 1, 9);
//
//                                // 打印十六进制值进行调试
//                                StringBuilder hexString = new StringBuilder();
//                                for (byte b : amountBytes) {
//                                    hexString.append(String.format("%02X ", b & 0xFF));
//                                }
//                                logger.info("Amount bytes (hex): {}", hexString.toString());
//
//                                // 根据日志输出的实际字节模式来解析
//                                long lamports;
//                                String hexValue = hexString.toString().trim();
//
//                                // 尝试更通用的解析方式 - 取后4个字节并以小端序读取
//                                byte[] relevantBytes = new byte[4];
//                                System.arraycopy(amountBytes, 3, relevantBytes, 0, 4);
//
//                                ByteBuffer buffer = ByteBuffer.wrap(relevantBytes);
//                                buffer.order(ByteOrder.LITTLE_ENDIAN);
//                                lamports = Integer.toUnsignedLong(buffer.getInt());
//
//                                // 转换为SOL (1 SOL = 10^9 lamports)
//                                BigDecimal solAmount = new BigDecimal(lamports).divide(new BigDecimal(1_000_000_000), 9, RoundingMode.HALF_UP);
//
//                                // 输出原始lamports值和SOL值
//                                logger.info("转账金额：{} lamports ({} SOL)", lamports, solAmount);
//                            } else {
//                                logger.info("系统指令类型未知的系统指令类型:{}", instructionType);
//                            }
//
//
//                        }
//                        case "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA" -> {
//                            logger.info("代币程序");
//
//                            int instructionType = decodedData[0] & 0xFF;
//                            logger.info("代币指令类型: {}", instructionType);
//                            switch (instructionType) {
//                                case 3:
//                                    logger.info("代币指令类型检测到Transfer指令:{}", instructionType);
//                                    logger.info("授权账户的token账户Source：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(0))));
//                                    logger.info("目标账户的token账户Destination：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(1))));
//                                    logger.info("授权账户Authority：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(2))));
//
//                                    // For Token transfers (case 3), amount is at bytes 1-8
//                                    byte[] tokenAmountBytes = Arrays.copyOfRange(decodedData, 1, 9);
//                                    long tokenRawAmount = ByteBuffer.wrap(tokenAmountBytes).order(ByteOrder.LITTLE_ENDIAN).getLong();
//                                    // 通常SPL Token有6位小数
//                                    int decimals = 6;
//                                    BigDecimal tokenAmount = new BigDecimal(BigInteger.valueOf(tokenRawAmount)).divide(BigDecimal.TEN.pow(decimals), decimals, RoundingMode.HALF_UP);
//                                    logger.info("Token转账金额：{} (原始值: {})", tokenAmount, tokenRawAmount);
//
//                                    break;
//                                case 12:
//                                    logger.info("代币指令类型检测到TransferChecked指令:{}", instructionType);
//                                    //transferChecked，需要注意transfer
//                                    logger.info("授权账户的token账户Source：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(0))));
//                                    logger.info("代币mint：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(1))));
//                                    logger.info("目标账户的token账户Destination：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(2))));
//                                    logger.info("授权账户Authority：{}", transaction.getTransaction().getMessage().getAccountKeys().get(Math.toIntExact(item.getAccounts().get(3))));
//
//                                    // For TransferChecked (case 12), amount is at bytes 1-8
//                                    byte[] tokenCheckedAmountBytes = Arrays.copyOfRange(decodedData, 1, 9);
//                                    long tokenCheckedRawAmount = ByteBuffer.wrap(tokenCheckedAmountBytes).order(ByteOrder.LITTLE_ENDIAN).getLong();
//                                    // TransferChecked中有明确的decimals值
//                                    int checkedDecimals = decodedData.length > 9 ? decodedData[9] & 0xFF : 6;
//                                    BigDecimal tokenCheckedAmount = new BigDecimal(BigInteger.valueOf(tokenCheckedRawAmount))
//                                        .divide(BigDecimal.TEN.pow(checkedDecimals), checkedDecimals, RoundingMode.HALF_UP);
//                                    logger.info("Token转账金额：{} (原始值: {}, 小数位: {})", tokenCheckedAmount, tokenCheckedRawAmount, checkedDecimals);
//
//                                    break;
//                                default:
//                                    logger.info("代币指令类型未知的代币指令类型:{}", instructionType);
//
//                            }
//
//                        }
//                        default -> {
//                            logger.info("未知程序");
//                            return;
//                        }
//                    }
//
//                    byte[] decode = Base58.decode(item.getData());
//                    System.out.println(Arrays.toString(decode));
//
//
//                });
//                System.out.println();
//            }
//
//
//        } catch (RpcException e) {
//            throw new RuntimeException(e);
//        }
//    }

}
