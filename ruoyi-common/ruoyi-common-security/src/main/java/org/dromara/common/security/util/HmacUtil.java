package org.dromara.common.security.util;

import cn.hutool.core.lang.UUID;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * HMAC工具类，用于生成和验证基于HMAC-SHA256的签名
 *
 * <AUTHOR>
 * @date 2025/4/15 10:30
 **/
@Component
@SuppressWarnings("unused")
public class HmacUtil {
    private static final Logger log = LoggerFactory.getLogger(HmacUtil.class);

    private static final String DEFAULT_SECRET = "sava-1024";
    private static final String ALGORITHM = "HmacSHA256";
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // HTTP头部常量
    public static final String HEADER_TIMESTAMP = "X-Meta-Timestamp";
    public static final String HEADER_NONCE = "X-Meta-Nonce";
    public static final String HEADER_SIGNATURE = "X-Meta-Signature";
    public static final String HEADER_PATH = "X-Meta-Path";

    // 配置项，可通过配置文件注入
    @Value("${security.hmac.secret:#{null}}")
    private String configSecret;

    @Value("${security.hmac.nonce-expire-minutes:5}")
    private long nonceExpireMinutes;

    @Value("${security.hmac.timestamp-diff-minutes:5}")
    private long timestampDiffMinutes;

    // 静态变量，便于在非Spring环境中使用
    private static String SECRET = DEFAULT_SECRET;
    private static long NONCE_EXPIRE_MS = TimeUnit.MINUTES.toMillis(5);
    private static long ALLOWED_TIMESTAMP_DIFF_MS = TimeUnit.MINUTES.toMillis(5);

    // 静态初始化块，确保在非Spring环境中也能正确初始化
    static {
        log.info("HmacUtil静态初始化完成 - 使用默认配置");
    }

    // 用于存储已用过的nonce，防止重放（生产环境建议用Redis等分布式缓存）
    private static final Map<String, Long> NONCE_CACHE = new ConcurrentHashMap<>();
    // 缓存Mac实例，避免重复创建
    private static final ThreadLocal<Mac> MAC_CACHE = ThreadLocal.withInitial(() -> {
        try {
            Mac mac = Mac.getInstance(ALGORITHM);
            mac.init(new SecretKeySpec(SECRET.getBytes(StandardCharsets.UTF_8), ALGORITHM));
            return mac;
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("初始化HMAC算法失败", e);
            throw new RuntimeException("初始化HMAC算法失败", e);
        }
    });

    /**
     * 签名数据实体类，用于封装签名相关的参数
     */
    @Setter
    @Getter
    public static class SignatureData {
        // Getter and Setter
        @JsonProperty("timestamp")
        private long timestamp;

        @JsonProperty("nonce")
        private String nonce;

        @JsonProperty("data")
        private String data;

        @JsonProperty("sign")
        private String sign;

        // 无参构造函数，用于反序列化
        public SignatureData() {
        }

        // 构造函数
        public SignatureData(String data) {
            this.timestamp = System.currentTimeMillis();
            this.nonce = UUID.randomUUID().toString(true);
            this.data = data;
        }

        // 构造函数
        public SignatureData(long timestamp, String nonce, String data, String sign) {
            this.timestamp = timestamp;
            this.nonce = nonce;
            this.data = data;
            this.sign = sign;
        }

        /**
         * 生成签名内容
         */
        public String buildSignContent() {
            return timestamp + nonce + data;
        }

        /**
         * 转换为Map
         */
        public Map<String, Object> toMap() {
            Map<String, Object> map = new HashMap<>(4);
            map.put("timestamp", timestamp);
            map.put("nonce", nonce);
            map.put("data", data);
            map.put("sign", sign);
            return map;
        }

        /**
         * 转换为JSON字符串
         */
        public String toJson() {
            try {
                return OBJECT_MAPPER.writeValueAsString(this);
            } catch (JsonProcessingException e) {
                log.error("JSON序列化失败", e);
                throw new RuntimeException("JSON序列化失败", e);
            }
        }

        /**
         * 从JSON字符串创建实例
         */
        public static SignatureData fromJson(String json) {
            try {
                return OBJECT_MAPPER.readValue(json, SignatureData.class);
            } catch (JsonProcessingException e) {
                log.error("JSON反序列化失败", e);
                throw new RuntimeException("JSON反序列化失败", e);
            }
        }
    }

    /**
     * HTTP头部签名数据实体类
     */
    @Getter
    @Setter
    public static class HttpHeaderSignature {
        private long timestamp;
        private String nonce;
        private String path;
        private String signature;

        public HttpHeaderSignature() {
            this.timestamp = System.currentTimeMillis();
            this.nonce = UUID.randomUUID().toString(true);
        }

        /**
         * 转换为HTTP头部Map
         */
        public Map<String, String> toHeaders() {
            Map<String, String> headers = new HashMap<>(4);
            headers.put(HEADER_TIMESTAMP, String.valueOf(timestamp));
            headers.put(HEADER_NONCE, nonce);
            if (path != null) {
                headers.put(HEADER_PATH, path);
            }
            if (signature != null) {
                headers.put(HEADER_SIGNATURE, signature);
            }
            return headers;
        }
    }

    @PostConstruct
    public void init() {
        // 只在配置有值时才覆盖默认值
        if (configSecret != null && !configSecret.isEmpty()) {
            SECRET = configSecret;
            log.info("HmacUtil加载配置密钥");
        }

        NONCE_EXPIRE_MS = TimeUnit.MINUTES.toMillis(nonceExpireMinutes);
        ALLOWED_TIMESTAMP_DIFF_MS = TimeUnit.MINUTES.toMillis(timestampDiffMinutes);

        log.info("HmacUtil Spring初始化完成 - nonceExpire: {}分钟, timestampDiff: {}分钟",
            nonceExpireMinutes, timestampDiffMinutes);
    }

    /**
     * 自动生成参数并签名
     *
     * @param data 要签名的数据
     * @return 包含签名及相关参数的对象
     */
    public static SignatureData sign(String data) {
        if (data == null) {
            throw new IllegalArgumentException("签名数据不能为空");
        }

        try {
            SignatureData signData = new SignatureData(data);
            String signContent = signData.buildSignContent();
            String sign = hmacSha256(signContent);
            signData.setSign(sign);
            return signData;
        } catch (Exception e) {
            log.error("生成签名参数失败", e);
            throw new RuntimeException("生成签名参数失败", e);
        }
    }

    /**
     * 自动生成参数并签名对象
     *
     * @param object 要签名的对象
     * @return 包含签名及相关参数的对象
     */
    public static SignatureData sign(Object object) {
        if (object == null) {
            throw new IllegalArgumentException("签名对象不能为空");
        }

        try {
            // 将对象序列化为JSON字符串
            String jsonData = objectToJson(object);
            // 使用现有的字符串签名方法
            return sign(jsonData);
        } catch (Exception e) {
            log.error("生成对象签名参数失败", e);
            throw new RuntimeException("生成对象签名参数失败", e);
        }
    }

    /**
     * 签名并生成JSON
     *
     * @param data 要签名的数据
     * @return 签名后的JSON字符串
     */
    public static String signToJson(String data) {
        return sign(data).toJson();
    }

    /**
     * 签名对象并生成JSON
     *
     * @param object 要签名的对象
     * @return 签名后的JSON字符串
     */
    public static String signToJson(Object object) {
        return sign(object).toJson();
    }

    /**
     * 自动生成参数并签名（原有方法，保持向后兼容）
     *
     * @param data 要签名的数据
     * @return 包含签名及相关参数的Map
     */
    public static Map<String, Object> buildSignedParams(String data) {
        return sign(data).toMap();
    }

    /**
     * 自动生成参数并签名对象
     *
     * @param object 要签名的对象
     * @return 包含签名及相关参数的Map
     */
    public static Map<String, Object> buildSignedParams(Object object) {
        return sign(object).toMap();
    }

    /**
     * 生成用于HTTP头部的签名
     *
     * @param path 请求路径
     * @param body 请求体(可以为null，用于GET请求)
     * @return HTTP头部签名对象
     */
    public static HttpHeaderSignature signForHttpHeader(String path, String body) {
        HttpHeaderSignature headerSignature = new HttpHeaderSignature();
        headerSignature.setPath(path);

        // 构建签名内容
        StringBuilder signContent = new StringBuilder();
        signContent.append(headerSignature.getTimestamp())
                  .append(headerSignature.getNonce())
                  .append(path);

        // 如果有请求体，添加请求体
        if (body != null && !body.isEmpty()) {
            signContent.append(body);
        }

        // 计算签名
        String signature = hmacSha256(signContent.toString());
        headerSignature.setSignature(signature);

        return headerSignature;
    }

    /**
     * 生成用于HTTP头部的签名，支持对象请求体
     *
     * @param path 请求路径
     * @param body 请求体对象(可以为null，用于GET请求)
     * @return HTTP头部签名对象
     */
    public static HttpHeaderSignature signForHttpHeader(String path, Object body) {
        // 将对象序列化为JSON字符串
        String bodyJson = body != null ? objectToJson(body) : null;
        // 使用现有的字符串签名方法
        return signForHttpHeader(path, bodyJson);
    }

    /**
     * 验证HTTP头部签名
     *
     * @param headers HTTP头部Map
     * @param path 请求路径
     * @param body 请求体(可以为null，用于GET请求)
     * @return 验证结果
     */
    public static boolean verifyHttpHeaderSignature(Map<String, String> headers, String path, String body) {
        // 检查必要的头部是否存在
        if (headers == null ||
            !headers.containsKey(HEADER_TIMESTAMP) ||
            !headers.containsKey(HEADER_NONCE) ||
            !headers.containsKey(HEADER_SIGNATURE)) {
            log.warn("验签头部不完整");
            return false;
        }

        try {
            long timestamp = Long.parseLong(headers.get(HEADER_TIMESTAMP));
            String nonce = headers.get(HEADER_NONCE);
            String signature = headers.get(HEADER_SIGNATURE);

            // 1. 检查timestamp是否过期
            long now = System.currentTimeMillis();
            if (Math.abs(now - timestamp) > ALLOWED_TIMESTAMP_DIFF_MS) {
                log.warn("签名时间戳超出允许范围: {} vs {}", timestamp, now);
                return false;
            }

            // 2. 检查nonce是否重复
            synchronized (NONCE_CACHE) {
                if (NONCE_CACHE.containsKey(nonce)) {
                    log.warn("检测到重复nonce: {}", nonce);
                    return false;
                }
                NONCE_CACHE.put(nonce, now);

                // 定期清理过期nonce
                if (NONCE_CACHE.size() % 100 == 0) {
                    NONCE_CACHE.entrySet().removeIf(e -> now - e.getValue() > NONCE_EXPIRE_MS);
                }
            }

            // 3. 构建签名内容并验证
            StringBuilder signContent = new StringBuilder();
            signContent.append(timestamp)
                      .append(nonce)
                      .append(path);

            // 如果有请求体，添加请求体
            if (body != null && !body.isEmpty()) {
                signContent.append(body);
            }

            String expectedSignature = hmacSha256(signContent.toString());
            return constantTimeEquals(expectedSignature, signature);

        } catch (Exception e) {
            log.error("验证HTTP头部签名失败", e);
            return false;
        }
    }

    /**
     * 验证HTTP头部签名，支持对象请求体
     *
     * @param headers HTTP头部Map
     * @param path 请求路径
     * @param body 请求体对象(可以为null，用于GET请求)
     * @return 验证结果
     */
    public static boolean verifyHttpHeaderSignature(Map<String, String> headers, String path, Object body) {
        // 将对象序列化为JSON字符串
        String bodyJson = body != null ? objectToJson(body) : null;
        // 使用现有的字符串验证方法
        return verifyHttpHeaderSignature(headers, path, bodyJson);
    }

    /**
     * 验证签名数据
     *
     * @param signData 签名数据对象
     * @return 验证结果
     */
    public static boolean verify(SignatureData signData) {
        if (signData == null || signData.getData() == null ||
            signData.getNonce() == null || signData.getSign() == null) {
            log.warn("验签参数不完整");
            return false;
        }

        try {
            long now = System.currentTimeMillis();

            // 1. 检查timestamp是否过期
            if (Math.abs(now - signData.getTimestamp()) > ALLOWED_TIMESTAMP_DIFF_MS) {
                log.warn("签名时间戳超出允许范围: {} vs {}", signData.getTimestamp(), now);
                return false;
            }

            // 2. 检查nonce是否重复（防重放）
            synchronized (NONCE_CACHE) {
                if (NONCE_CACHE.containsKey(signData.getNonce())) {
                    log.warn("检测到重复nonce: {}", signData.getNonce());
                    return false;
                }
                NONCE_CACHE.put(signData.getNonce(), now);

                // 清理过期nonce (只在添加新nonce时进行清理，避免频繁清理)
                if (NONCE_CACHE.size() % 100 == 0) {
                    NONCE_CACHE.entrySet().removeIf(e -> now - e.getValue() > NONCE_EXPIRE_MS);
                }
            }

            // 3. 校验签名
            String signContent = signData.buildSignContent();
            String expectedSign = hmacSha256(signContent);
            if (!constantTimeEquals(expectedSign, signData.getSign())) {
                log.warn("签名验证失败");
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("验证签名失败", e);
            return false;
        }
    }

    /**
     * 从JSON字符串验证签名
     *
     * @param json JSON格式的签名数据
     * @return 验证结果
     */
    public static boolean verifyFromJson(String json) {
        try {
            SignatureData signData = SignatureData.fromJson(json);
            return verify(signData);
        } catch (Exception e) {
            log.error("从JSON验证签名失败", e);
            return false;
        }
    }

    /**
     * 验签并自动检测timestamp和nonce（原有方法，保持向后兼容）
     *
     * @param data 签名数据
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param sign 签名
     * @return 验证结果
     */
    public static boolean verify(String data, long timestamp, String nonce, String sign) {
        SignatureData signData = new SignatureData(timestamp, nonce, data, sign);
        return verify(signData);
    }

    /**
     * 验签对象并自动检测timestamp和nonce
     *
     * @param object 签名对象
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param sign 签名
     * @return 验证结果
     */
    public static boolean verify(Object object, long timestamp, String nonce, String sign) {
        // 将对象序列化为JSON字符串
        String jsonData = objectToJson(object);
        // 使用现有的字符串验证方法
        return verify(jsonData, timestamp, nonce, sign);
    }

    /**
     * 使用外部提供的密钥进行HMAC-SHA256签名
     *
     * @param secret 密钥
     * @param message 消息内容
     * @return 签名结果Base64编码
     */
    public static String hmacSha256(String secret, String message) {
        if (secret == null || message == null) {
            throw new IllegalArgumentException("密钥和消息不能为空");
        }

        try {
            Mac mac = Mac.getInstance(ALGORITHM);
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), ALGORITHM));
            byte[] hash = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("HMAC签名失败", e);
            throw new RuntimeException("HMAC签名失败", e);
        }
    }

    /**
     * 使用默认密钥进行HMAC-SHA256签名
     *
     * @param message 消息内容
     * @return 签名结果Base64编码
     */
    private static String hmacSha256(String message) {
        if (message == null) {
            throw new IllegalArgumentException("消息不能为空");
        }

        try {
            Mac mac = MAC_CACHE.get();
            byte[] hash = mac.doFinal(message.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hash);
        } catch (Exception e) {
            log.error("使用默认密钥HMAC签名失败", e);
            throw new RuntimeException("HMAC签名失败", e);
        }
    }

    /**
     * 将对象序列化为JSON字符串
     *
     * @param object 需要序列化的对象
     * @return JSON字符串
     */
    public static String objectToJson(Object object) {
        if (object == null) {
            return null;
        }

        // 如果已经是字符串，则直接返回
        if (object instanceof String) {
            return (String) object;
        }

        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("对象序列化为JSON失败", e);
            throw new RuntimeException("对象序列化为JSON失败", e);
        }
    }

    /**
     * 将JSON字符串反序列化为对象
     *
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 对象类型
     * @return 反序列化的对象
     */
    public static <T> T jsonToObject(String json, Class<T> clazz) {
        if (json == null || json.isEmpty() || clazz == null) {
            return null;
        }

        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化为对象失败", e);
            throw new RuntimeException("JSON反序列化为对象失败", e);
        }
    }

    /**
     * 常量时间比较两个字符串，防止时序攻击
     *
     * @param a 第一个字符串
     * @param b 第二个字符串
     * @return 是否相等
     */
    private static boolean constantTimeEquals(String a, String b) {
        if (a == null || b == null) {
            return false;
        }

        byte[] aBytes = a.getBytes(StandardCharsets.UTF_8);
        byte[] bBytes = b.getBytes(StandardCharsets.UTF_8);

        if (aBytes.length != bBytes.length) {
            return false;
        }

        int result = 0;
        for (int i = 0; i < aBytes.length; i++) {
            result |= aBytes[i] ^ bBytes[i];
        }
        return result == 0;
    }

    /**
     * 清除nonce缓存
     */
    public static void clearNonceCache() {
        NONCE_CACHE.clear();
    }

    // 测试方法
    public static void main(String[] args) {
        try {
            String data = "hello world";

            // 测试实体类方式
            System.out.println("=== 测试实体类方式 ===");
            SignatureData signData = sign(data);
            System.out.println("签名数据对象: " + signData.toJson());
            boolean result = verify(signData);
            System.out.println("验签结果: " + result);

            // 测试JSON序列化和反序列化
            System.out.println("\n=== 测试JSON方式 ===");
            String json = signToJson(data);
            System.out.println("签名JSON: " + json);
            boolean jsonResult = verifyFromJson(json);
            System.out.println("JSON验签结果: " + jsonResult);

            // 测试原有Map方式 (向后兼容)
            System.out.println("\n=== 测试原有Map方式 ===");
            Map<String, Object> params = buildSignedParams(data);
            System.out.println("请求参数: " + params);
            boolean mapResult = verify(
                (String) params.get("data"),
                ((Number) params.get("timestamp")).longValue(),
                (String) params.get("nonce"),
                (String) params.get("sign")
            );
            System.out.println("Map验签结果: " + mapResult);

            // 测试HTTP头部签名方式
            System.out.println("\n=== 测试HTTP头部签名方式 ===");
            String path = "/api/resource";
            String body = "{\"key\":\"value\"}";

            // 客户端生成签名头部
            HttpHeaderSignature headerSignature = signForHttpHeader(path, body);
            Map<String, String> headers = headerSignature.toHeaders();
            System.out.println("HTTP头部: " + headers);

            // 服务端验证头部签名
            boolean headerResult = verifyHttpHeaderSignature(headers, path, body);
            System.out.println("HTTP头部验签结果: " + headerResult);

            // 测试GET请求(无请求体)
            System.out.println("\n=== 测试GET请求签名(无请求体) ===");
            HttpHeaderSignature getSignature = signForHttpHeader("/api/users", null);
            Map<String, String> getHeaders = getSignature.toHeaders();
            System.out.println("GET请求头部: " + getHeaders);
            boolean getResult = verifyHttpHeaderSignature(getHeaders, "/api/users", null);
            System.out.println("GET请求验签结果: " + getResult);

            // 测试对象签名方式
            System.out.println("\n=== 测试对象签名方式 ===");
            TestObject testObj = new TestObject("测试", 100);
            SignatureData objSignData = sign(testObj);
            System.out.println("对象签名数据: " + objSignData.toJson());
            boolean objResult = verify(objSignData);
            System.out.println("对象验签结果: " + objResult);

            // 测试对象HTTP头部签名
            System.out.println("\n=== 测试对象HTTP头部签名方式 ===");
            HttpHeaderSignature objHeaderSignature = signForHttpHeader("/api/objects", testObj);
            Map<String, String> objHeaders = objHeaderSignature.toHeaders();
            System.out.println("对象HTTP头部: " + objHeaders);
            boolean objHeaderResult = verifyHttpHeaderSignature(objHeaders, "/api/objects", testObj);
            System.out.println("对象HTTP头部验签结果: " + objHeaderResult);

            // 测试重放攻击防护
            System.out.println("\n=== 测试重放攻击防护 ===");
            boolean replayResult = verify(signData);
            System.out.println("重放攻击测试结果(应为false): " + replayResult);

            // 测试HTTP头部重放防护
            boolean headerReplayResult = verifyHttpHeaderSignature(headers, path, body);
            System.out.println("HTTP头部重放防护测试结果(应为false): " + headerReplayResult);
        } catch (Exception e) {
            log.error("测试失败", e);
        }
    }

    /**
     * 用于测试的简单对象类
     */
    static class TestObject {
        private String name;
        private int value;

        public TestObject() {
        }

        public TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getValue() {
            return value;
        }

        public void setValue(int value) {
            this.value = value;
        }
    }
}
